{"hitWithRecoil": "¡{{pokemon<PERSON>ame}} tambi<PERSON>\nse ha hecho daño!", "cutHpPowerUpMove": "¡{{pokemon<PERSON>ame}} sacrifica sus PS\npara mejorar su movimiento!", "absorbedElectricity": "¡{{pokemonName}} está acumulando electricidad!", "switchedStatChanges": "¡{{pokemonName}} intercambió los cambios\nde características con el objetivo!", "switchedTwoStatChanges": "¡{{pokemonName}} ha intercambiado los cambios en {{firstStat}} y {{secondStat}} con los del objetivo!", "switchedStat": "¡{{pokemonName}} cambia su {{stat}}\npor la de su objetivo!", "sharedGuard": "¡{{pokemonName}} suma su capacidad defensiva a la del objetivo y la reparte equitativamente!", "sharedPower": "¡{{pokemonName}} suma su capacidad ofensiva a la del objetivo y la reparte equitativamente!", "shiftedStats": "¡{{pokemonName}} ha intercambiado el valor\nde su {{statToSwitch}} por el de su {{statToSwitchWith}}!", "goingAllOutForAttack": "¡{{pokemon<PERSON>ame}} lo ha dado todo!", "regainedHealth": "¡{{pokemon<PERSON>ame}} ha recuperado PS!", "keptGoingAndCrashed": "¡{{pokemon<PERSON>ame}} ha fallado y se ha caído al suelo!", "fled": "¡{{pokemon<PERSON><PERSON>}} ha huido!", "cannotBeSwitchedOut": "¡{{pokemonName}} no puede dejar el combate!", "swappedAbilitiesWithTarget": "¡{{pokemon<PERSON>ame}} ha intercambiado\nsu habilidad con la de su objetivo!", "coinsScatteredEverywhere": "¡Hay monedas por todas partes!", "attackedByItem": "¡{{pokemonName}} sufre daño\npor su {{itemName}}!", "whippedUpAWhirlwind": "¡{{pokemon<PERSON>ame}} se prepara\npara lanzar una borrasca!", "flewUpHigh": "¡{{pokemon<PERSON>ame}} voló alto!", "tookInSunlight": "¡{{pokemon<PERSON><PERSON>}} ha absorbido luz solar!", "dugAHole": "¡{{pokemon<PERSON>ame}} se ha ocultado bajo tierra!", "loweredItsHead": "¡{{pokemon<PERSON>ame}} ha agachado la cabeza!", "isGlowing": "¡Un intenso halo rodea\na {{pokemonName}}!", "bellChimed": "Ha repicado una campana.", "foresawAnAttack": "¡{{pokemon<PERSON>ame}} ha previsto el ataque!", "isTighteningFocus": "¡{{pokemonName}} está reforzando su concentración!", "lostFocus": "¡{{pokemon<PERSON>ame}} ha perdido la concentración y no puede atacar!", "hidUnderwater": "¡{{pokemon<PERSON>ame}} se ha ocultado bajo el agua!", "soothingAromaWaftedThroughArea": "Un aroma balsámico flota en el aire…", "sprangUp": "¡{{pokemon<PERSON>ame}} ha saltado muy alto!", "choseDoomDesireAsDestiny": "¡{{pokemon<PERSON>ame}} ha elegido Deseo Oculto para el futuro!", "vanishedInstantly": "¡{{pokemon<PERSON>ame}} ha desaparecido en un abrir y cerrar de ojos!", "tookTargetIntoSky": "¡{{pokemonName}} se ha llevado\na {{targetName}} por los aires!", "becameCloakedInFreezingLight": "¡Una luz fría envuelve\na {{pokemonName}}!", "becameCloakedInFreezingAir": "¡Una ráfaga gélida envuelve\na {{pokemonName}}!", "isChargingPower": "¡{{pokemonName}} está acumulando energía!", "burnedItselfOut": "¡El fuego interior de {{pokemonName}}\nse ha extinguido!", "startedHeatingUpBeak": "¡{{pokemon<PERSON><PERSON>}} empieza\na calentar su pico!", "setUpShellTrap": "¡{{pokemonName}} ha activado\nla Coraza Trampa!", "isOverflowingWithSpacePower": "¡{{pokemon<PERSON>ame}} rebosa\nenergía cósmica!", "usedUpAllElectricity": "¡{{pokemon<PERSON><PERSON>}} ha descargado\ntoda su electricidad!", "stoleItem": "¡{{pokemonName}} robó el objeto\n{{itemName}} de {{targetName}}!", "incineratedItem": "¡{{pokemonName}} ha incinerado\nla {{itemName}} de {{targetName}}!", "knockedOffItem": "¡{{itemName}} de {{targetName}} ha caído al suelo!", "tookMoveAttack": "¡{{pokemonName}} ha sido alcanzado\npor {{moveName}}!", "cutOwnHpAndMaximizedStat": "¡{{pokemonName}} ha sacrificado algunos PS\ny ha aumentado su {{statName}} al máximo!", "copiedStatChanges": "¡{{pokemonName}} ha copiado los cambios\nde características de {{targetName}}!", "magnitudeMessage": "¡Magnitud: {{magnitude}}!", "tookAimAtTarget": "¡{{pokemonName}} tiene a {{targetName}} en su punto de mira!", "transformedIntoType": "¡{{pokemonName}} ha cambiado\na tipo {{typeName}}!", "copiedMove": "¡{{pokemonName}} ha copiado\n{{moveName}}!", "sketchedMove": "¡{{pokemonName}} ha usado Esquema\npara copiar {{moveName}}!", "acquiredAbility": "¡La habilidad de {{pokemonName}}\nha cambiado a {{abilityName}}!", "copiedTargetAbility": "¡{{pokemonName}} ha copiado la habilidad\n{{abilityName}} de {{targetName}}!", "transformedIntoTarget": "¡{{pokemonName}} se ha transformad\nen {{targetName}}!", "tryingToTakeFoeDown": "¡{{pokemon<PERSON>ame}} intenta que\nel rival sufra su mismo destino!", "addType": "¡{{pokemonName}} ahora también es\nde tipo {{typeName}}!", "cannotUseMove": "¡{{pokemonName}} no puede usar\n{{moveName}}!", "healHp": "¡{{pokemonName}} recuperó sus PS!", "sacrificialFullRestore": "¡El deseo de curación se ha hecho realidad\npara {{pokemonName}}!", "invertStats": "¡Se han invertido los cambios de características\nde {{pokemonName}}!", "resetStats": "¡Se han anulado todos los cambios de características\nde {{pokemonName}}!", "statEliminated": "¡Los cambios en estadísticas fueron eliminados!", "faintCountdown": "{{pokemonName}} se debilitar<PERSON> dentro\nde {{turnCount}} turnos.", "copyType": "¡{{pokemonName}} ahora es del mismo tipo\nque {{targetPokemonName}}!", "suppressAbilities": "¡Se ha anulado la habilidad\nde {{pokemonName}}!", "revivalBlessing": "¡{{pokemon<PERSON>ame}} ha revivido!", "swapArenaTags": "¡{{pokemon<PERSON>ame}} ha intercambiado\nlos efectos del terreno de combate!", "chillyReception": "{{pokemon<PERSON>ame}} se prepara\npara contar un chiste malo…", "exposedMove": "¡{{pokemonName}} ha identificado\n{{targetPokemonName}}!", "safeguard": "¡{{targetName}} está protegido\npor Velo Sagrado!", "restBecameHealthy": "¡{{pokemon<PERSON><PERSON>}} se ha recuperado\ntras dormir un poco!", "substituteOnOverlap": "¡{{poke<PERSON><PERSON><PERSON>}} ya tiene un sustituto!", "substituteNotEnoughHp": "¡Está demasiado débil\npara crear un sustituto!", "afterYou": "¡{{targetName}} ha decidido\naprovechar la oportunidad!", "combiningPledge": "¡Los dos movimientos se han unido!\n¡Es un movimiento combinado!", "awaitingPledge": "El {{userPokemonName}} está esperando\nal movimiento de {{allyPokemonName}}…", "corrosiveGasItem": "¡{{pokemonName}} ha derretido el objeto {{itemName}} del {{targetName}}!", "instructingMove": "¡{{targetPokemonName}} sigue el mandato de {{userPokemonName}} y repite su último movimiento!", "lunarDanceRestore": "¡Un místico halo de luz de luna\nenvuelve a {{pokemonName}}!", "stealPositiveStats": "¡{{pokemonName}} se ha apropiado de las mejoras en las características de {{targetName}}!", "naturePowerUse": "¡El movimiento Adaptación de {{pokemonName}}\nha pasado a ser {{moveName}}!", "forceLast": "¡{{targetPokemonName}} ha retrasado su turno!", "splash": "¡Pero no ha tenido ningún efecto!", "fallDown": "¡{{targetPokemonName}} ha sido\nderribado y ha caído al suelo!", "celebrate": "¡Felicidades, {{playerName}}!", "struggle": "¡A {{pokemon<PERSON>ame}} no le quedan\nmás movimientos!"}