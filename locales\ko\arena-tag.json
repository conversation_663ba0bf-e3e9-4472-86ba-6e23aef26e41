{"mistOnAdd": "양측 모두가\n흰안개에 둘러싸였다!", "mistOnAddPlayer": "우리 편은\n흰안개에 둘러싸였다!", "mistOnAddEnemy": "상대는\n흰안개에 둘러싸였다!", "mistOnRemove": "양측 모두를 감싸던\n흰안개가 없어졌다!", "mistOnRemovePlayer": "우리 편을 감싸던\n흰안개가 없어졌다!", "mistOnRemoveEnemy": "상대를 감싸던\n흰안개가 없어졌다!", "mistApply": "{{pokemonNameWithAffix}}[[는]]\n흰안개가 지켜 주고 있다!", "reflectOnAdd": "양측 모두가 리플렉터로\n물리공격에 강해졌다!", "reflectOnAddPlayer": "우리 편은 리플렉터로\n물리공격에 강해졌다!", "reflectOnAddEnemy": "상대는 리플렉터로\n물리공격에 강해졌다!", "lightScreenOnAdd": "양측 모두가 빛의장막으로\n특수공격에 강해졌다!", "lightScreenOnAddPlayer": "우리 편은 빛의장막으로\n특수공격에 강해졌다!", "lightScreenOnAddEnemy": "상대는 빛의장막으로\n특수공격에 강해졌다!", "auroraVeilOnAdd": "양측 모두가 오로라베일로\n물리공격과 특수공격에 강해졌다!", "auroraVeilOnAddPlayer": "우리 편은 오로라베일로\n물리공격과 특수공격에 강해졌다!", "auroraVeilOnAddEnemy": "상대는 오로라베일로\n물리공격과 특수공격에 강해졌다!", "conditionalProtectOnAdd": "양측 모두를\n{{moveName}}[[가]] 보호하고 있다!", "conditionalProtectOnAddPlayer": "우리 편 주변을\n{{moveName}}[[가]] 보호하고 있다!", "conditionalProtectOnAddEnemy": "상대 주변을\n{{moveName}}[[가]] 보호하고 있다!", "conditionalProtectApply": "{{pokemonNameWithAffix}}[[를]]\n{{moveName}}[[가]] 지켜주고 있다!", "matBlockOnAdd": "{{pokemonNameWithAffix}}[[는]]\n마룻바닥세워막기를 노리고 있다!", "matBlockApply": "{{attackName}}[[는]]\n마룻바닥세워막기로 막혔다!", "noCritOnAdd": "주술의 힘으로\n양측 모두의 급소가 숨겨졌다!", "noCritOnAddPlayer": "주술의 힘으로\n우리 편의 급소가 숨겨졌다!", "noCritOnAddEnemy": "주술의 힘으로\n상대의 의 급소가 숨겨졌다!", "noCritOnRemove": "양축 모두의\n주술이 풀렸다!", "noCritOnRemovePlayer": "우리 편의 주술이 풀렸다!", "noCritOnRemoveEnemy": "상대의 주술이 풀렸다!", "wishTagOnAdd": "{{pokemonNameWithAffix}}의\n희망사항이 이루어졌다!", "mudSportOnAdd": "전기의 위력이 약해졌다!", "mudSportOnRemove": "흙놀이의 효과가\n없어졌다!", "waterSportOnAdd": "불꽃의 위력이 약해졌다!", "waterSportOnRemove": "물놀이의 효과가\n없어졌다!", "plasmaFistsOnAdd": "전기 입자가 쏟아졌다!", "spikesOnAdd": "양측 모두의 발밑에\n압정이 뿌려졌다!", "spikesOnAddPlayer": "우리 편의 발밑에\n압정이 뿌려졌다!", "spikesOnAddEnemy": "상대의 발밑에\n압정이 뿌려졌다!", "spikesOnRemove": "양측 발밑의\n압정이 사라졌다!", "spikesOnRemovePlayer": "우리 편 발밑의\n압정이 사라졌다!", "spikesOnRemoveEnemy": "상대 발밑의\n압정이 사라졌다!", "spikesActivateTrap": "{{pokemonNameWithAffix}}[[는]]\n압정뿌리기의 데미지를 입었다!", "toxicSpikesOnAdd": "양측 모두의 발밑에\n독압정이 뿌려졌다!", "toxicSpikesOnAddPlayer": "우리편의 발밑에\n독압정이 뿌려졌다!", "toxicSpikesOnAddEnemy": "상대의 발밑에\n독압정이 뿌려졌다!", "toxicSpikesOnRemove": "양측 발밑의 \n독압정이 사라졌다!", "toxicSpikesOnRemovePlayer": "우리 편 발밑의\n독압정이 사라졌다!", "toxicSpikesOnRemoveEnemy": "상대 발밑의\n독압정이 사라졌다!", "stealthRockOnAdd": "양측 주변에\n뾰족한 바위가 떠다니기 시작했다!", "stealthRockOnAddPlayer": "우리 편 주변에\n뾰족한 바위가 떠다니기 시작했다!", "stealthRockOnAddEnemy": "상대 주변에\n뾰족한 바위가 떠다니기 시작했다!", "stealthRockOnRemove": "양측 주변의\n스텔스록이 사라졌다!", "stealthRockOnRemovePlayer": "우리 편 주변의\n스텔스록이 사라졌다!", "stealthRockOnRemoveEnemy": "상대 주변의\n스텔스록이 사라졌다!", "stealthRockActivateTrap": "{{pokemonNameWithAffix}}에게\n뾰족한 바위가 박혔다!", "stickyWebOnAdd": "양측 모두의 발밑에\n끈적끈적네트가 펼쳐졌다!", "stickyWebOnAddPlayer": "우리 편 발밑에\n끈적끈적네트가 펼쳐졌다!", "stickyWebOnAddEnemy": "상대 발밑에\n끈적끈적네트가 펼쳐졌다!", "stickyWebOnRemove": "양측 모두의 발밑의\n끈적끈적네트가 사라졌다!", "stickyWebOnRemovePlayer": "우리 편 발밑의\n끈적끈적네트가 사라졌다!", "stickyWebOnRemoveEnemy": "상대 편 발밑의\n끈적끈적네트가 사라졌다!", "stickyWebActivateTrap": "{{pokemonNameWithAffix}}[[는]]\n끈적끈적네트에 걸렸다!", "trickRoomOnAdd": "{{pokemonNameWithAffix}}[[는]]\n시공을 뒤틀었다!", "trickRoomOnRemove": "뒤틀린 시공이 원래대로 되돌아왔다!", "wonderRoomOnAdd": "방어와 특수방어가 바뀌는\n공간을 만들어 냈다!", "wonderRoomOnRemove": "원더룸이 해제되어, 방어와 특수방어가 원래대로 되돌아왔다!", "gravityOnAdd": "중력이 강해졌다!", "gravityOnRemove": "중력이 원래대로 되돌아왔다!", "tailwindOnAdd": "양측 모두에게\n순풍이 불기 시작했다!", "tailwindOnAddPlayer": "우리 편에게\n순풍이 불기 시작했다!", "tailwindOnAddEnemy": "상대에게\n순풍이 불기 시작했다!", "tailwindOnRemove": "순풍이 멈췄다!", "tailwindOnRemovePlayer": "우리 편의\n순풍이 멈췄다!", "tailwindOnRemoveEnemy": "상대의\n순풍이 멈췄다!", "happyHourOnAdd": "모두 행복한 기분에\n휩싸였다!", "happyHourOnRemove": "기분이 원래대로 돌아왔다.", "safeguardOnAdd": "필드 전체가 신비의 베일에 둘러싸였다!", "safeguardOnAddPlayer": "우리 편은 신비의 베일에 둘러싸였다!", "safeguardOnAddEnemy": "상대 편은 신비의 베일에 둘러싸였다!", "safeguardOnRemove": "필드를 감싸던 신비의 베일이 없어졌다!", "safeguardOnRemovePlayer": "우리 편을 감싸던 신비의 베일이 없어졌다!", "safeguardOnRemoveEnemy": "상대 편을 감싸던 신비의 베일이 없어졌다!", "fireGrassPledgeOnAdd": "필드 주변이\n불바다에 둘러싸였다!", "fireGrassPledgeOnAddPlayer": "우리 편 주변이\n불바다에 둘러싸였다!", "fireGrassPledgeOnAddEnemy": "상대 주변이\n불바다에 둘러싸였다!", "fireGrassPledgeOnRemove": "양측 주변의\n불바다가 사라졌다!", "fireGrassPledgeOnRemovePlayer": "우리 편 주변의\n불바다가 사라졌다!", "fireGrassPledgeOnRemoveEnemy": "상대 주변의\n불바다가 사라졌다!", "fireGrassPledgeLapse": "{{pokemonNameWithAffix}}[[는]]\n불바다의 데미지를 입었다!", "waterFirePledgeOnAdd": "하늘에 무지개가 걸렸다!", "waterFirePledgeOnAddPlayer": "우리 편 하늘에 무지개가 걸렸다!", "waterFirePledgeOnAddEnemy": "상대 하늘에 무지개가 걸렸다!", "waterFirePledgeOnRemove": "양측 하늘에서 무지개가 사라졌다!", "waterFirePledgeOnRemovePlayer": "우리 편 하늘에서 무지개가 사라졌다!", "waterFirePledgeOnRemoveEnemy": "상대 하늘에서 무지개가 사라졌다!", "grassWaterPledgeOnAdd": "필드에 습지초원이 펼쳐졌다!", "grassWaterPledgeOnAddPlayer": "우리 편 주변에\n습지초원이 펼쳐졌다!", "grassWaterPledgeOnAddEnemy": "상대 주변에\n습지초원이 펼쳐졌다!", "grassWaterPledgeOnRemove": "양측 주변의\n습지초원이 사라졌다!", "grassWaterPledgeOnRemovePlayer": "우리 편 주변의\n습지초원이 사라졌다!", "grassWaterPledgeOnRemoveEnemy": "상대 주변의\n습지초원이 사라졌다!", "fairyLockOnAdd": "다음 턴은 도망갈 수 없다!", "neutralizingGasOnAdd": "주위가 화학변화가스로 가득 찼다!", "neutralizingGasOnRemove": "화학변화가스의 효과가 사라졌다!"}