{"mistOnAdd": "Os dois lados ficaram\nenvoltos em névoa!", "mistOnAddPlayer": "Sua equipe ficou\nenvolta em névoa!", "mistOnAddEnemy": "A equipe adversária ficou\nenvolta em névoa!", "mistOnRemove": "Os dois lados não estão mais\nprotegidos pela névoa!", "mistOnRemovePlayer": "Sua equipe não está mais\nprotegida pela névoa!", "mistOnRemoveEnemy": "A equipe adversária não está mais\nprotegida pela névoa!", "mistApply": "{{pokemonNameWithAffix}} está protegido\npela névoa!", "reflectOnAdd": "Reflect tornou ambos os lados\nmais fortes contra ataques físicos!", "reflectOnAddPlayer": "Reflect tornou sua equipe\nmais forte contra ataques físicos!", "reflectOnAddEnemy": "Reflect tornou a equipe adversária\nmais forte contra ataques físicos!", "lightScreenOnAdd": "Light Screen tornou ambos os lados\nmais fortes contra ataques especiais!", "lightScreenOnAddPlayer": "Light Screen tornou sua equipe\nmais forte contra ataques especiais!", "lightScreenOnAddEnemy": "Light Screen tornou a equipe adversária\nmais forte contra ataques especiais!", "auroraVeilOnAdd": "Aurora Veil tornou ambos os lados mais\nfortes contra ataques físicos e especiais!", "auroraVeilOnAddPlayer": "Aurora Veil fortaleceu seu lado\ncontra ataques físicos e especiais!", "auroraVeilOnAddEnemy": "Aurora Veil tornou o lado adversário mais\nforte contra ataques físicos e especiais!", "conditionalProtectOnAdd": "{{moveName}} protegeu os dois lados!", "conditionalProtectOnAddPlayer": "{{moveName}} protegeu sua equipe!", "conditionalProtectOnAddEnemy": "{{moveName}} protegeu a\nequipe adversária!", "conditionalProtectApply": "{{moveName}} protegeu {{pokemonNameWithAffix}}!", "matBlockOnAdd": "{{pokemonNameWithAffix}} pretende levantar um tapete\npara bloquear ataques!", "matBlockApply": "{{attackName}} foi bloqueado pelo tapete levantado!", "noCritOnAdd": "<PERSON> <PERSON> protegeu ambas\nas equipes de golpes críticos!", "noCritOnAddPlayer": "O Lucky Chant protegeu sua\nequipe de golpes críticos!", "noCritOnAddEnemy": "O <PERSON> Chant protegeu a equipe\nadversária de golpes críticos!", "noCritOnRemove": "<PERSON> <PERSON> as\nequipes perdeu o efeito!", "noCritOnRemovePlayer": "O Lucky Chant da sua\nequipe perdeu o efeito!", "noCritOnRemoveEnemy": "O Lucky Chant da equipe\nadversária perdeu o efeito!", "wishTagOnAdd": "O desejo de {{pokemonNameWithAffix}}\nfoi concedido!", "mudSportOnAdd": "O poder de movimentos elétricos foi enfraquecido!", "mudSportOnRemove": "Os efeitos de Mud Sport\nsumiram.", "waterSportOnAdd": "O poder de movimentos de fogo foi enfraquecido!", "waterSportOnRemove": "Os efeitos de Water Sport\nsumiram.", "plasmaFistsOnAdd": "Um dilúvio de íons chove sobre o campo de batalha!", "spikesOnAdd": "Espinhos foram espalhados pelo\nchão ao redor das duas equipes!", "spikesOnAddPlayer": "Espinhos foram espalhados pelo\nchão ao redor da sua equipe!", "spikesOnAddEnemy": "Espinhos foram espalhados pelo\nchão ao redor da equipe adversária!", "spikesOnRemove": "Os espinhos desapareceram\ndo chão!", "spikesOnRemovePlayer": "Os espinhos desapareceram\ndo chão ao redor da sua equipe!", "spikesOnRemoveEnemy": "Os espinhos desapareceram do\nchão ao redor da equipe adversária!", "spikesActivateTrap": "{{pokemonNameWithAffix}} foi ferido\npelos espinhos!", "toxicSpikesOnAdd": "E<PERSON>in<PERSON> venenosos foram espalhados\npelo chão ao redor das duas equipes!", "toxicSpikesOnAddPlayer": "<PERSON><PERSON>in<PERSON> venenosos foram espalhados\npelo chão ao redor da sua equipe!", "toxicSpikesOnAddEnemy": "<PERSON><PERSON>in<PERSON> venenosos foram espalhados\npelo chão ao redor da equipe adversária!", "toxicSpikesOnRemove": "Os espinhos venenosos\ndesapareceram do chão!", "toxicSpikesOnRemovePlayer": "Os espinhos venenosos desapareceram\ndo chão ao redor da sua equipe!", "toxicSpikesOnRemoveEnemy": "Os espinhos venenosos desapareceram\ndo chão ao redor da equipe adversária!", "stealthRockOnAdd": "Pedras pontiagudas flutuam no ar\nao redor das duas equipes!", "stealthRockOnAddPlayer": "Pedras pontiagudas flutuam no ar\nao redor da sua equipe!", "stealthRockOnAddEnemy": "Pedras pontiagudas flutuam no ar\nao redor da equipe adversária!", "stealthRockOnRemove": "As pedras pontiagudas desapareceram\nao redor das duas equipes!", "stealthRockOnRemovePlayer": "As pedras pontiagudas desapareceram\nao redor da sua equipe!", "stealthRockOnRemoveEnemy": "As pedras pontiagudas desapareceram\nda equipe adversária!", "stealthRockActivateTrap": "{{pokemonNameWithAffix}} foi atingido\npor pedras pontiagudas!", "stickyWebOnAdd": "Uma teia pegajosa foi colocada\nno chão ao redor das duas equipes!", "stickyWebOnAddPlayer": "Uma teia pegajosa foi colocada\nno chão ao redor da sua equipe!", "stickyWebOnAddEnemy": "Uma teia pegajosa foi colocada no\nchão ao redor da equipe adversária!", "stickyWebOnRemove": "A teia pegajosa desapareceu\ndo chão!", "stickyWebOnRemovePlayer": "A teia pegajosa desapareceu\ndo chão ao seu redor!", "stickyWebOnRemoveEnemy": "A teia pegajosa desapareceu\ndo chão ao redor da equipe adversária!", "stickyWebActivateTrap": "{{pokemonNameWithAffix}} ficou preso em uma teia pegajosa!", "trickRoomOnAdd": "{{pokemonNameWithAffix}} distorceu\nas dimensões!", "trickRoomOnRemove": "As dimensões distorcidas\nretornaram ao normal!", "wonderRoomOnAdd": "Isso criou uma área bizarra na qual\nos atributos de Def. e Def. Esp. são trocados!", "wonderRoomOnRemove": "O efeito da Wonder Room passou, e os atributos de Def. e Def. Esp. voltaram ao normal!", "gravityOnAdd": "A gravidade aumentou!", "gravityOnRemove": "A gravidade retornou ao normal!", "tailwindOnAdd": "O vento soprou a favor de ambas as equipes!", "tailwindOnAddPlayer": "O Tailwind soprou de trás de\nsua equipe!", "tailwindOnAddEnemy": "O Tailwind soprou de trás da\nequipe adversária!", "tailwindOnRemove": "O vento favorável acabou!", "tailwindOnRemovePlayer": "O Tailwind de sua equipe acabou!", "tailwindOnRemoveEnemy": "O Tailwind da equipe adversária acabou!", "happyHourOnAdd": "Todos foram envolvidos por uma atmosfera alegre!", "happyHourOnRemove": "A atmosfera retornou ao normal.", "safeguardOnAdd": "O campo de batalha está envolto num véu místico!", "safeguardOnAddPlayer": "Sua equipe se envolveu num véu místico!", "safeguardOnAddEnemy": "A equipe adversária se envolveu num véu místico!", "safeguardOnRemove": "O campo de batalha não está mais protegido por Safeguard!", "safeguardOnRemovePlayer": "Sua equipe não está mais protegido por Safeguard!", "safeguardOnRemoveEnemy": "A equipe adversária não está mais protegido por Safeguard!", "fireGrassPledgeOnAdd": "Um mar de fogo envolveu o campo de batalha!", "fireGrassPledgeOnAddPlayer": "Um mar de fogo envolveu sua equipe!", "fireGrassPledgeOnAddEnemy": "Um mar de fogo envolveu a equipe adversária!", "fireGrassPledgeOnRemove": "O mar de fogo desapareceu!", "fireGrassPledgeOnRemovePlayer": "O mar de fogo ao redor da sua equipe desapareceu!", "fireGrassPledgeOnRemoveEnemy": "O mar de fogo ao redor da equipe adversária desapareceu!", "fireGrassPledgeLapse": "{{pokemonNameWithAffix}} foi ferido pelo mar de fogo!", "waterFirePledgeOnAdd": "Um arco-íris apareceu no céu!", "waterFirePledgeOnAddPlayer": "Um arco-íris apareceu no céu do lado da sua equipe!", "waterFirePledgeOnAddEnemy": "Um arco-íris apareceu no céu do lado da equipe adversária!", "waterFirePledgeOnRemove": "O arco-íris desapareceu!", "waterFirePledgeOnRemovePlayer": "O arco-íris do lado da sua equipe desapareceu!", "waterFirePledgeOnRemoveEnemy": "O arco-íris do lado do time adversário desapareceu!", "grassWaterPledgeOnAdd": "Um pântano envolveu o campo de batalha!", "grassWaterPledgeOnAddPlayer": "Um pântano envolveu sua equipe!", "grassWaterPledgeOnAddEnemy": "Um pântano envolveu a equipe adversária!", "grassWaterPledgeOnRemove": "O pântano desapareceu!", "grassWaterPledgeOnRemovePlayer": "O pântano ao redor da sua equipe desapareceu!", "grassWaterPledgeOnRemoveEnemy": "O pântano ao redor da equipe adversária desapareceu!", "fairyLockOnAdd": "Ninguém conseguirá fugir durante o próximo turno!", "neutralizingGasOnAdd": "O Neutralizing Gas de {{pokemonNameWithAffix}} se espalhou pelo campo de batalha!", "neutralizingGasOnRemove": "Os efeitos do gás neutralizante acabaram!"}