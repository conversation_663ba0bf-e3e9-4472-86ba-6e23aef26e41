{"mistOnAdd": "相手も　味方も\n白い霧に　包まれた！", "mistOnAddPlayer": "味方は\n白い霧に　包まれた！", "mistOnAddEnemy": "相手は\n白い霧に　包まれた！", "mistOnRemove": "味方と　相手を　包んでいた\n白い霧が　なくなった！", "mistOnRemovePlayer": "味方を　包んでいた\n白い霧が　なくなった！", "mistOnRemoveEnemy": "相手を　包んでいた\n白い霧が　なくなった！", "mistApply": "{{pokemonNameWithAffix}}は\n白い霧に　守られている！", "reflectOnAdd": "味方と　相手は　リフレクターで\n物理に　強くなった！", "reflectOnAddPlayer": "味方は　リフレクターで\n物理に　強くなった！", "reflectOnAddEnemy": "相手は　リフレクターで\n物理に　強くなった！", "lightScreenOnAdd": "味方と　相手は　ひかりのかべで\n特殊に　強くなった！", "lightScreenOnAddPlayer": "味方は　ひかりのかべで\n特殊に　強くなった！", "lightScreenOnAddEnemy": "相手は　ひかりのかべで\n特殊に　強くなった！", "auroraVeilOnAdd": "味方と　相手は　オーロラベールで\n物理と　特殊に　強くなった！", "auroraVeilOnAddPlayer": "味方は　オーロラベールで\n物理と　特殊に　強くなった！", "auroraVeilOnAddEnemy": "相手は　オーロラベールで\n物理と　特殊に　強くなった！", "conditionalProtectOnAdd": "味方と　相手は\n{{moveName}}に　守られた！", "conditionalProtectOnAddPlayer": "見方が　{{moveName}}に　守られた！", "conditionalProtectOnAddEnemy": "相手が　{{moveName}}に　守られた！", "conditionalProtectApply": "{{pokemonNameWithAffix}}が\n{{moveName}}に　守られた！", "matBlockOnAdd": "{{pokemonNameWithAffix}}は\nたたみがえしを　ねらっている！", "matBlockApply": "{{attackName}}は\nたたみがえしで　防がれた！", "noCritOnAdd": "おまじないの　力で\n味方と　相手の　急所が　隠れた！", "noCritOnAddPlayer": "おまじないの　力で\n味方の急所が　隠れた！", "noCritOnAddEnemy": "おまじないの　力で\n相手の急所が　隠れた！", "noCritOnRemove": "味方と　相手の　\nおまじないが解けた！", "noCritOnRemovePlayer": "味方の　おまじないが解けた！", "noCritOnRemoveEnemy": "相手の　おまじないが解けた！", "wishTagOnAdd": "{{pokemonNameWithAffix}}の\nねがいごとが　かなった！", "mudSportOnAdd": "電気の威力が　弱まった！", "mudSportOnRemove": "どろあそびの　効果が　なくなった！", "waterSportOnAdd": "炎の威力が　弱まった！", "waterSportOnRemove": "みずあそびの　効果が　なくなった！", "plasmaFistsOnAdd": "電子のシャワーが　降りそそいだ！", "spikesOnAdd": "味方と　相手の　足下に\nまきびしが　散らばった！", "spikesOnAddPlayer": "味方の　足下に\nまきびしが　散らばった！", "spikesOnAddEnemy": "相手の　足下に\nまきびしが　散らばった！", "spikesOnRemove": "足下の\nまきびしが　消え去った！", "spikesOnRemovePlayer": "味方の　足下の\nまきびしが　消え去った！", "spikesOnRemoveEnemy": "相手の　足下の\nまきびしが　消え去った！", "spikesActivateTrap": "{{pokemonNameWithAffix}}は\nまきびしの　ダメージを　受けた！", "toxicSpikesOnAdd": "味方と　相手の　足下に\nどくびしが　散らばった！", "toxicSpikesOnAddPlayer": "味方の　足下に\nどくびしが　散らばった！", "toxicSpikesOnAddEnemy": "相手の　足下に\nどくびしが　散らばった！", "toxicSpikesOnRemove": "足下の\nどくびしが　消え去った！", "toxicSpikesOnRemovePlayer": "味方の　足下の\nどくびしが　消え去った！", "toxicSpikesOnRemoveEnemy": "相手の　足下の\nどくびしが　消え去った！", "stealthRockOnAdd": "場の　周りに\nとがった岩が　ただよい始めた！", "stealthRockOnAddPlayer": "味方の　周りに\nとがった岩が　ただよい始めた！", "stealthRockOnAddEnemy": "相手の　周りに\nとがった岩が　ただよい始めた！", "stealthRockOnRemove": "場の　周りの\nステルスロックが　消え去った！", "stealthRockOnRemovePlayer": "味方の　周りの\nステルスロックが　消え去った！", "stealthRockOnRemoveEnemy": "相手の　周りの\nステルスロックが　消え去った！", "stealthRockActivateTrap": "{{pokemonNameWithAffix}}に\nとがった岩が　食い込んだ！", "stickyWebOnAdd": "味方と　相手の　足下に\nねばねばネットが　広がった！", "stickyWebOnAddPlayer": "味方の　足下に\nねばねばネットが　広がった！", "stickyWebOnAddEnemy": "相手の　足下に\nねばねばネットが　広がった！", "stickyWebOnRemove": "足下の\nねばねばネットが　消え去った！", "stickyWebOnRemovePlayer": "味方の　足下の\nねばねばネットが　消え去った！", "stickyWebOnRemoveEnemy": "相手の　足下の\nねばねばネットが　消え去った！", "stickyWebActivateTrap": "{{pokemonNameWithAffix}}は\nねばねばネットに　ひっかかった", "trickRoomOnAdd": "{{pokemonNameWithAffix}}は\n時空を　ゆがめた！", "trickRoomOnRemove": "ゆがんだ　時空が　元に戻った！", "wonderRoomOnAdd": "防御と　特防が　入れかわる\n空間を　作り出した！", "wonderRoomOnRemove": "ワンダールームが　解除され\n防御と　特防が　元に戻った！", "gravityOnAdd": "じゅうりょくが　強くなった！", "gravityOnRemove": "じゅうりょくが　元に戻った！", "tailwindOnAdd": "味方と　相手に\n追い風が　吹き始めた！", "tailwindOnAddPlayer": "味方に\n追い風が　吹き始めた！", "tailwindOnAddEnemy": "相手に\n追い風が　吹き始めた！", "tailwindOnRemove": "追い風が　止んだ！", "tailwindOnRemovePlayer": "味方の　追い風が　止んだ！", "tailwindOnRemoveEnemy": "相手の　追い風が　止んだ！", "happyHourOnAdd": "みんなが　ハッピーな気分に\n包まれた！", "happyHourOnRemove": "みんなの　気分が　元に戻った。", "safeguardOnAdd": "場の全体は　神秘のベールに　包まれた！", "safeguardOnAddPlayer": "味方は　神秘のベールに　包まれた！", "safeguardOnAddEnemy": "相手は　神秘のベールに　包まれた！", "safeguardOnRemove": "場の全体を　包んでいた\n神秘のベールが　なくなった！", "safeguardOnRemovePlayer": "味方を　包んでいた\n神秘のベールが　なくなった！", "safeguardOnRemoveEnemy": "相手を　包んでいた\n神秘のベールが　なくなった！", "fireGrassPledgeOnAdd": "場の　周りが　火の海に　包まれた！", "fireGrassPledgeOnAddPlayer": "味方の　周りが　火の海に　包まれた！", "fireGrassPledgeOnAddEnemy": "相手の　周りが　火の海に　包まれた！", "fireGrassPledgeOnRemove": "火の海が　消え去った！", "fireGrassPledgeOnRemovePlayer": "味方の　周りの\n火の海が　消え去った！", "fireGrassPledgeOnRemoveEnemy": "相手の　周りの\n火の海が　消え去った！", "fireGrassPledgeLapse": "{{pokemonNameWithAffix}}は\n火の海の　ダメージを受けた！", "waterFirePledgeOnAdd": "場の空に　にじが　かかった！", "waterFirePledgeOnAddPlayer": "味方の空に　にじが　かかった！", "waterFirePledgeOnAddEnemy": "相手の空に　にじが　かかった！", "waterFirePledgeOnRemove": "にじが　消えた！", "waterFirePledgeOnRemovePlayer": "味方の空から　にじが　消えた！", "waterFirePledgeOnRemoveEnemy": "相手の空から　にじが　消えた！", "grassWaterPledgeOnAdd": "場の　周りに　湿原が　広がった！", "grassWaterPledgeOnAddPlayer": "味方の　周りに　湿原が　広がった！", "grassWaterPledgeOnAddEnemy": "相手の　周りに　湿原が　広がった！", "grassWaterPledgeOnRemove": "湿原が　消え去った！", "grassWaterPledgeOnRemovePlayer": "味方の　周りの\n湿原が　消え去った！", "grassWaterPledgeOnRemoveEnemy": "相手の　周りの\n湿原が　消え去った！", "fairyLockOnAdd": "次のターンは　逃げられない！", "neutralizingGasOnAdd": "あたりに　かがくへんかガスが　充満した！", "neutralizingGasOnRemove": "かがくへんかガスの　効果が　切れた！"}