{"mistOnAdd": "¡Una neblina ha cubierto a ambos bandos!", "mistOnAddPlayer": "¡Una neblina ha cubierto a tu bando!", "mistOnAddEnemy": "¡Una neblina ha cubierto al bando rival!", "mistOnRemove": "Ha desaparecido el efecto de la neblina en ambos bandos.", "mistOnRemovePlayer": "Ha desaparecido el efecto de la neblina en tu bando.", "mistOnRemoveEnemy": "Ha desaparecido el efecto de la neblina en el bando rival.", "mistApply": "¡{{pokemonNameWithAffix}} está protegido por la neblina!", "reflectOnAdd": "¡Reflejo ha aumentado la resistencia de ambos bandos ante los ataques físicos!", "reflectOnAddPlayer": "¡Reflejo ha aumentado la resistencia de tu bando ante los ataques físicos!", "reflectOnAddEnemy": "¡Reflejo ha aumentado la resistencia del bando rival ante los ataques físicos!", "lightScreenOnAdd": "¡Pantalla de Luz ha aumentado la resistencia de ambos bandos ante los ataques especiales!", "lightScreenOnAddPlayer": "¡Pantalla de Luz ha aumentado la resistencia de tu bando ante los ataques especiales!", "lightScreenOnAddEnemy": "¡Pantalla de Luz ha aumentado la resistencia del bando rival ante los ataques especiales!", "auroraVeilOnAdd": "¡Velo Aurora ha aumentado la resistencia de ambos bandos ante los ataques físicos y especiales!", "auroraVeilOnAddPlayer": "¡Velo Aurora ha aumentado la resistencia de tu bando ante los ataques físicos y especiales!", "auroraVeilOnAddEnemy": "¡Velo Aurora ha aumentado la resistencia del bando rival ante los ataques físicos y especiales!", "conditionalProtectOnAdd": "¡{{moveName}} protege a ambos bandos!", "conditionalProtectOnAddPlayer": "¡{{moveName}} protege a tu bando!", "conditionalProtectOnAddEnemy": "¡{{moveName}} protege al bando rival!", "conditionalProtectApply": "¡{{pokemonNameWithAffix}} ha sido protegido\npor {{moveName}}!", "matBlockOnAdd": "¡{{pokemonNameWithAffix}} va a usar\nun tatami para bloquear ataques!", "matBlockApply": "¡Escudo Tatami neutraliza {{attackName}}!", "noCritOnAdd": "¡Conjuro protege a ambos bandos de los golpes críticos!", "noCritOnAddPlayer": "¡Conjuro protege a tu bando de los golpes críticos!", "noCritOnAddEnemy": "¡Conjuro protege al bando rival de los golpes críticos!", "noCritOnRemove": "El efecto de Conjuro en ambos bandos se ha desvanecido.", "noCritOnRemovePlayer": "El efecto de Conjuro en tu bando se ha desvanecido.", "noCritOnRemoveEnemy": "El efecto de Conjuro en el bando rival se ha desvanecido.", "wishTagOnAdd": "¡El deseo de {{pokemonNameWithAffix}}\nse ha hecho realidad!", "mudSportOnAdd": "¡Se han debilitado los ataques\nde tipo Eléctrico!", "mudSportOnRemove": "Chapoteo Lodo ha dejado de surtir efecto.", "waterSportOnAdd": "¡Se han debilitado los ataques\nde tipo Fuego!", "waterSportOnRemove": "Hidrochorro ha dejado de surtir efecto.", "plasmaFistsOnAdd": "¡Una lluvia de electrones cae sobre\nel terreno de combate!", "spikesOnAdd": "¡Ambos bandos están rodeados de púas!", "spikesOnAddPlayer": "¡Tu bando está rodeado de púas!", "spikesOnAddEnemy": "¡El bando rival está rodeado de púas!", "spikesOnRemove": "Las púas lanzadas han desaparecido.\n", "spikesOnRemovePlayer": "Las púas lanzadas a tu bando han desaparecido.", "spikesOnRemoveEnemy": "Las púas lanzadas al bando rival han desaparecido.", "spikesActivateTrap": "¡Las púas han herido\na {{pokemonNameWithAffix}}!", "toxicSpikesOnAdd": "¡Ambos bandos están rodeados de púas tóxicas!", "toxicSpikesOnAddPlayer": "¡Tu bando está rodeado de púas tóxicas!", "toxicSpikesOnAddEnemy": "¡El bando rival está rodeado de púas tóxicas!", "toxicSpikesOnRemove": "Las púas tóxicas lanzadas han desaparecido.", "toxicSpikesOnRemovePlayer": "Las púas tóxicas lanzadas a tu bando han desaparecido.", "toxicSpikesOnRemoveEnemy": "Las púas tóxicas lanzadas al bando rival han desaparecido.", "stealthRockOnAdd": "¡Ambos bandos están rodeados de piedras puntiagudas!", "stealthRockOnAddPlayer": "¡Tu bando está rodeado de piedras puntiagudas!", "stealthRockOnAddEnemy": "¡El bando rival está rodeado de piedras puntiagudas!", "stealthRockOnRemove": "Las piedras puntiagudas lanzadas han desaparecido.", "stealthRockOnRemovePlayer": "Las piedras puntiagudas lanzadas a tu bando han desaparecido.", "stealthRockOnRemoveEnemy": "Las piedras puntiagudas lanzadas al bando rival han desaparecido.", "stealthRockActivateTrap": "¡Unas piedras puntiagudas han dañado\na {{pokemonNameWithAffix}}!", "stickyWebOnAdd": "¡Una red viscosa se extiende a los pies de ambos bandos!", "stickyWebOnAddPlayer": "¡Una red viscosa se extiende a los pies de tu bando!", "stickyWebOnAddEnemy": "¡Una red viscosa se extiende a los pies del bando rival!", "stickyWebOnRemove": "La red viscosa que rodeaba a ambos bandos ha desaparecido.", "stickyWebOnRemovePlayer": "La red viscosa que rodeaba a tu bando ha desaparecido.", "stickyWebOnRemoveEnemy": "La red viscosa que rodeaba al bando rival ha desaparecido.", "stickyWebActivateTrap": "¡{{pokemonNameWithAffix}} ha caído en una red viscosa!", "trickRoomOnAdd": "¡{{pokemonNameWithAffix}} ha alterado\nlas dimensiones!", "trickRoomOnRemove": "Se han restaurado las dimensiones alteradas.", "wonderRoomOnAdd": "¡Se ha creado un espacio en el que la Defensa y la Defensa Especial se invierten!", "wonderRoomOnRemove": "Los efectos de Zona Extraña sobre la Defensa y la Defensa Especial han desaparecido.", "gravityOnAdd": "¡La gravedad se ha incrementado!", "gravityOnRemove": "La gravedad ha vuelto a su estado normal.", "tailwindOnAdd": "¡El viento sopla a favor de ambos bandos!", "tailwindOnAddPlayer": "¡El viento sopla a favor de tu bando!", "tailwindOnAddEnemy": "¡El viento sopla a favor del bando rival!", "tailwindOnRemove": "Ha dejado de soplar el viento.", "tailwindOnRemovePlayer": "Ha dejado de soplar el viento que favorecía a tu equipo.", "tailwindOnRemoveEnemy": "Ha dejado de soplar el viento que favorecía al bando rival.", "happyHourOnAdd": "¡La felicidad se respira en el aire!", "happyHourOnRemove": "La felicidad ya no se respira en el aire.", "safeguardOnAdd": "¡Todos los Pokémon están protegidos por Velo Sagrado!", "safeguardOnAddPlayer": "¡Tu equipo se ha protegido con Velo Sagrado!", "safeguardOnAddEnemy": "¡El equipo enemigo se ha protegido con Velo Sagrado!", "safeguardOnRemove": "¡Velo Sagrado dejó de hacer efecto!", "safeguardOnRemovePlayer": "El efecto de Velo Sagrado en tu equipo se ha disipado.", "safeguardOnRemoveEnemy": "El efecto de Velo Sagrado en el equipo enemigo se ha disipado.", "fireGrassPledgeOnAdd": "¡El terreno fué envuelto en un mar de llamas!", "fireGrassPledgeOnAddPlayer": "¡Tu equipo se ve rodeado por un mar de llamas!", "fireGrassPledgeOnAddEnemy": "¡El equipo rival se ve rodeado por un mar de llamas!", "fireGrassPledgeOnRemove": "El mar de llamas ha desaparecido.", "fireGrassPledgeOnRemovePlayer": "El mar de llamas que rodeaba a tu bando ha desaparecido.", "fireGrassPledgeOnRemoveEnemy": "El mar de llamas que rodeaba al bando rival ha desaparecido.", "fireGrassPledgeLapse": "¡{{pokemonNameWithAffix}} ha resultado herido\npor un mar de llamas!", "waterFirePledgeOnAdd": "¡Ha aparecido un arcoíris en el cielo!", "waterFirePledgeOnAddPlayer": "¡Ha aparecido un arcoíris sobre tu equipo!", "waterFirePledgeOnAddEnemy": "¡Ha aparecido un arcoíris sobre el equipo rival!", "waterFirePledgeOnRemove": "El arcoíris ha desaparecido.", "waterFirePledgeOnRemovePlayer": "El arcoíris sobre tu bando ha desaparecido.", "waterFirePledgeOnRemoveEnemy": "El arcoíris sobre el bando rival ha desaparecido.", "grassWaterPledgeOnAdd": "¡El terreno fué envuelto en un pantano!", "grassWaterPledgeOnAddPlayer": "¡Ha aparecido un pantano alrededor de tu equipo!", "grassWaterPledgeOnAddEnemy": "¡Ha aparecido un pantano alrededor del equipo rival!", "grassWaterPledgeOnRemove": "El pantano ha desaparecido.\n", "grassWaterPledgeOnRemovePlayer": "El pantano que rodeaba a tu bando ha desaparecido.", "grassWaterPledgeOnRemoveEnemy": "El pantano que rodeaba al bando rival ha desaparecido.", "fairyLockOnAdd": "Nadie podrá huir durante el próximo turno.", "neutralizingGasOnAdd": "¡Un gas reactivo se propaga por toda la zona!", "neutralizingGasOnRemove": "El gas reactivo se ha disipado."}