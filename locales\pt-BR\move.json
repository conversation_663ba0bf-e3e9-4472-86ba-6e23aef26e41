{"pound": {"name": "Pound", "effect": "O alvo é golpeado com uma pata, uma cauda longa, ou com algo desse tipo."}, "karateChop": {"name": "<PERSON><PERSON>", "effect": "O alvo é atacado com um golpe cortante. Golpes críticos ocorrem mais facilmente."}, "doubleSlap": {"name": "Double Slap", "effect": "O alvo é estapeado repetidamente, de duas a cinco vezes seguidas."}, "cometPunch": {"name": "Comet Punch", "effect": "O alvo é atingido com uma sequência de socos que acertam de duas a cinco vezes seguidas."}, "megaPunch": {"name": "Mega Punch", "effect": "O alvo é atingido por um soco desferido com grande força muscular."}, "payDay": {"name": "Pay Day", "effect": "Várias moedas são lançadas no alvo para causar dano. O Treinador recebe o dinheiro após a batalha."}, "firePunch": {"name": "Fire Punch", "effect": "O alvo é atingido por um punho flamejante. <PERSON><PERSON> pode deixar o alvo queimado."}, "icePunch": {"name": "Ice Punch", "effect": "O alvo é atingido por um punho gelado. <PERSON><PERSON> pode deixar o alvo congelado."}, "thunderPunch": {"name": "Thunder Punch", "effect": "O alvo é atingido por um punho eletrificado. <PERSON><PERSON> tamb<PERSON>m pode deixar o alvo paralisado."}, "scratch": {"name": "<PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON><PERSON> du<PERSON>, pontiagudas e afiadas rasgam o alvo para causar dano."}, "viseGrip": {"name": "<PERSON><PERSON>", "effect": "O alvo é agarrado e espremido de ambos os lados para causar dano."}, "guillotine": {"name": "Guillotine", "effect": "Um ataque violento e destruidor com grandes pinças. Se o golpe acertar, o alvo desmaiará instantaneamente."}, "razorWind": {"name": "Razor Wind", "effect": "Neste ataque de dois turnos, lâminas de vento golpeiam Pokémon adversários no segundo turno. Golpes críticos ocorrem mais facilmente."}, "swordsDance": {"name": "Swords Dance", "effect": "Uma dança frenética para elevar o espírito de luta. Aumenta bruscamente o Ataque do usuário."}, "cut": {"name": "Cut", "effect": "O alvo é cortado com uma foice ou garra."}, "gust": {"name": "<PERSON><PERSON>", "effect": "Uma rajada de vento é levantada por asas e lançada no alvo para causar dano."}, "wingAttack": {"name": "Wing Attack", "effect": "O alvo é atingido por asas grandes e imponentes, amplamente abertas para causar dano."}, "whirlwind": {"name": "Whirlwind", "effect": "O alvo é soprado para fora da batalha, dando lugar a outro Pokémon. Em batalhas selvagens, a batalha termina caso seja contra um único Pokémon."}, "fly": {"name": "Fly", "effect": "O usuário levanta vôo e ataca o alvo no próximo turno."}, "bind": {"name": "Bind", "effect": "Um longo corpo ou tentáculos são utilizados para prender o alvo e espremê-lo por quatro ou cinco turnos."}, "slam": {"name": "Slam", "effect": "O alvo é atingido com uma longa cauda, vinhas ou algo parecido para infligir dano."}, "vineWhip": {"name": "Vine Whip", "effect": "O usuário utiliza-se de vinhas finas como chicote para infligir dano."}, "stomp": {"name": "<PERSON><PERSON><PERSON>", "effect": "O alvo é pisoteado por um grande pé. <PERSON><PERSON> também pode fazer o alvo hesitar."}, "doubleKick": {"name": "Double Kick", "effect": "O alvo é atingido rapidamente com um chute duas vezes seguidas usando ambos os pés."}, "megaKick": {"name": "Mega Kick", "effect": "O alvo é atingido por um chute desferido com grande força muscular."}, "jumpKick": {"name": "Jump Kick", "effect": "O usuário pula alto, depois golpeia com um chute. Se o chute erra, o usuário se fere."}, "rollingKick": {"name": "Rolling Kick", "effect": "O usuário desfere um rápido chute giratório. <PERSON><PERSON> tamb<PERSON>m pode fazer o alvo hesitar."}, "sandAttack": {"name": "Sand Attack", "effect": "Areia é lançada no rosto do alvo, reduzindo sua Precisão."}, "headbutt": {"name": "Headbutt", "effect": "O usuário direciona sua cabeça e ataca, avançando diretamente sobre o alvo. <PERSON><PERSON> também pode fazer o alvo hesitar."}, "hornAttack": {"name": "Horn Attack", "effect": "O alvo é perfurado por um chifre pontudo e afiado para infligir dano."}, "furyAttack": {"name": "Fury Attack", "effect": "O alvo é perfurado repetidamente por um chifre ou bico, de duas a cinco vezes seguidas."}, "hornDrill": {"name": "Horn Drill", "effect": "O usuário perfura o alvo com um chifre que gira como uma broca. Se o golpe acertar, o alvo desmaia instantaneamente."}, "tackle": {"name": "Tackle", "effect": "Um ataque físico cujo o usuário vai para cima do alvo e lhe atinge com todo o seu corpo."}, "bodySlam": {"name": "Body Slam", "effect": "O usuário se lança para cima do alvo com todo o peso de seu corpo. Isso pode deixar o alvo paralisado."}, "wrap": {"name": "Wrap", "effect": "Um longo corpo, vin<PERSON> ou algo assim, são usados para embrulhar e apertar o alvo por quatro ou cinco turnos."}, "takeDown": {"name": "Take Down", "effect": "Uma investida corporal imprudente para golpear o alvo. <PERSON><PERSON> tamb<PERSON>m fere um pouco o usuário."}, "thrash": {"name": "<PERSON><PERSON><PERSON>", "effect": "O usuário fica furioso e ataca com violência de dois a três turnos. O usuário então se torna confuso."}, "doubleEdge": {"name": "Double-Edge", "effect": "Uma investida imprudente e muito perigosa. <PERSON><PERSON> tamb<PERSON>m fere bastante o usuário."}, "tailWhip": {"name": "Tail Whip", "effect": "O usuário balança sua cauda de maneira fofa, baixando a guarda do Pokémon adversário e diminuindo sua Defesa."}, "poisonSting": {"name": "Poison Sting", "effect": "O usuário perfura o alvo com um ferrão venenoso. <PERSON><PERSON> também pode envenenar o alvo."}, "twineedle": {"name": "Twineedle", "effect": "O usuário causa dano duas vezes seguidas, perfurando o alvo com dois ferrões. <PERSON><PERSON> também pode envenenar o alvo."}, "pinMissile": {"name": "Pin Missile", "effect": "Espinhos afiados são lançados no alvo em rápida sucessão. Eles acertam de duas a cinco vezes seguidas."}, "leer": {"name": "<PERSON><PERSON>", "effect": "O usuário lança um olhar intimidador no Pokémon oponente, reduzindo sua Defesa."}, "bite": {"name": "Bite", "effect": "O alvo é mordido ferozmente com presas afiadas. <PERSON><PERSON> tamb<PERSON>m pode fazer o alvo hesitar."}, "growl": {"name": "<PERSON><PERSON><PERSON>", "effect": "O usuário rosna de maneira agradável, baixando a guarda do Pokémon adversário. <PERSON><PERSON> diminui o Ataque do oponente."}, "roar": {"name": "Roar", "effect": "O alvo se assusta, retorna para a sua Poké Bola e um outro Pokémon toma o seu lugar. O combate é encerrado contra um único Pokémon selvagem."}, "sing": {"name": "Sing", "effect": "Uma suave canção de ninar é cantada com uma voz calma, colocando o alvo em sono profundo."}, "supersonic": {"name": "Supersonic", "effect": "O usuário gera estranhas ondas sonoras de seu corpo que confundem o alvo."}, "sonicBoom": {"name": "Sonic Boom", "effect": "O alvo é atingido com uma onda de choque destrutiva que sempre causa dano de 20 PS."}, "disable": {"name": "Disable", "effect": "Por quatro turnos, este movimento impede que o alvo utilize o último movimento usado por ele."}, "acid": {"name": "Acid", "effect": "Os Pokémon adversários são atacados com um jato de um forte ácido. <PERSON>so pode diminuir a Defesa Especial."}, "ember": {"name": "Ember", "effect": "O alvo é atacado com pequenas chamas. Também pode deixar o alvo com uma queimadura."}, "flamethrower": {"name": "Flamethrower", "effect": "O usuário queima o alvo com uma grande explosão de fogo. Também pode deixar o alvo com uma queimadura."}, "mist": {"name": "Mist", "effect": "O usuário esconde a si mesmo e seus aliados em uma neblina branca que impede que seus atributos sejam reduzidos por cinco turnos."}, "waterGun": {"name": "Water Gun", "effect": "O alvo é atingido por um disparo forte de água."}, "hydroPump": {"name": "Hydro Pump", "effect": "O alvo é atingido por um enorme volume de água lançado sob uma forte pressão."}, "surf": {"name": "Surf", "effect": "O usuário ataca tudo ao seu redor, inundando os arredores com uma onda gigante."}, "iceBeam": {"name": "Ice Beam", "effect": "O alvo é atingido por um raio de energia congelante. <PERSON><PERSON> tamb<PERSON>m pode deixar o alvo congelado."}, "blizzard": {"name": "Blizzard", "effect": "Uma enorme nevasca é invocada para atacar o Pokémon oponente. Também pode deixar o alvo congelado."}, "psybeam": {"name": "Psybeam", "effect": "O alvo é atacado por um feixe peculiar. <PERSON><PERSON> também pode deixar o alvo confuso."}, "bubbleBeam": {"name": "Bubble Beam", "effect": "Um jato de bolhas é borrifado com intensidade no alvo. <PERSON><PERSON> também pode diminuir a Velocidade do alvo."}, "auroraBeam": {"name": "Aurora Beam", "effect": "O alvo é atingido por um raio colorido como o arco-íris. <PERSON><PERSON> também pode diminuir o Ataque do alvo."}, "hyperBeam": {"name": "Hyper Beam", "effect": "O alvo é atingido por um raio poderoso. O usuário não poderá se mover no próximo turno."}, "peck": {"name": "<PERSON>", "effect": "O alvo é atingido por um bico ou chifre pontudo."}, "drillPeck": {"name": "<PERSON><PERSON>", "effect": "Um ataque giratório com um bico afiado que age como uma broca."}, "submission": {"name": "Submission", "effect": "O usuário agarra o alvo e, imprudentemente, mergulha em direção ao chão. <PERSON>so também fere um pouco o usuário."}, "lowKick": {"name": "Low Kick", "effect": "Um poderoso chute baixo que derruba o alvo. Quanto mais pesado o alvo for, maior o poder do movimento."}, "counter": {"name": "Counter", "effect": "Um movimento de retaliação que neutraliza qualquer ataque físico, causando o dobro do dano recebido."}, "seismicToss": {"name": "Seismic Toss", "effect": "O alvo é lançado usando o poder da gravidade. Isso causa dano igual ao nível do usuário."}, "strength": {"name": "Strength", "effect": "O alvo é atingido por um soco dado com o máximo de força."}, "absorb": {"name": "Absorb", "effect": "Um ataque que drena nutrientes. O usuário recupera PS pela metade do dano infligido ao alvo."}, "megaDrain": {"name": "Mega Drain", "effect": "Um ataque que drena nutrientes. O usuário recupera PS pela metade do dano infligido ao alvo."}, "leechSeed": {"name": "<PERSON><PERSON> Seed", "effect": "Uma semente é plantada no alvo. Isso rouba alguns pontos de PS do alvo a cada turno."}, "growth": {"name": "Growth", "effect": "O corpo do usuário cresce de uma vez só, aumentando seu Ataque e Ataque Especial."}, "razorLeaf": {"name": "Razor Leaf", "effect": "Folhas superafiadas são lançadas para cortar os Pokémon adversários. Golpes críticos ocorrem mais facilmente."}, "solarBeam": {"name": "Solar Beam", "effect": "Neste ataque de dois turnos, o usuário absorve luz, então dispara um raio focalizado no próximo turno."}, "poisonPowder": {"name": "<PERSON><PERSON>der", "effect": "O usuário espalha uma nuvem de poeira tóxica que envenena o alvo."}, "stunSpore": {"name": "Stun Spore", "effect": "O usuário espalha uma nuvem de esporos entorpecentes que paralisam o alvo."}, "sleepPowder": {"name": "Sleep Powder", "effect": "O usuário espalha uma grande nuvem de pó sonífero ao redor do alvo."}, "petalDance": {"name": "Petal Dance", "effect": "O usuário ataca o alvo espalhando pétalas de dois a três turnos. O usuário então fica confuso."}, "stringShot": {"name": "String Shot", "effect": "O Pokémon adversário é enrolado com uma seda expelida da boca do usuário, o que reduz duramente sua Velocidade."}, "dragonRage": {"name": "Dragon Rage", "effect": "Este ataque atinge o alvo com uma onda de choque de pura fúria. Este ataque sempre causa 40 PS de dano."}, "fireSpin": {"name": "Fire Spin", "effect": "O alvo fica preso dentro de um vórtice feroz de fogo que se prolonga por quatro ou cinco turnos."}, "thunderShock": {"name": "Thunder Shock", "effect": "Um choque elétrico que cai sobre o alvo causando dano. <PERSON><PERSON> também pode deixar o alvo paralisado."}, "thunderbolt": {"name": "Thunderbolt", "effect": "Uma forte explosão elétrica que cai sobre o alvo. Também pode deixar o alvo com paralisia."}, "thunderWave": {"name": "Thunder Wave", "effect": "O usuário lança um choque elétrico fraco que paralisa o alvo."}, "thunder": {"name": "Thunder", "effect": "Um raio cruel despenca no alvo para causar dano. <PERSON><PERSON> também pode deixar o alvo com paralisia."}, "rockThrow": {"name": "Rock Throw", "effect": "Para atacar, o usuário pega uma pequena rocha e joga no alvo."}, "earthquake": {"name": "Earthquake", "effect": "O usuário desencadeia um terremoto que atinge todos os Pokémon ao seu redor."}, "fissure": {"name": "Fissure", "effect": "O usuário abre uma fissura no chão e joga o alvo nela. O alvo desmaiará instantaneamente se esse ataque acertar."}, "dig": {"name": "Dig", "effect": "O usuário se entoca, então ataca no próximo turno."}, "toxic": {"name": "Toxic", "effect": "Um movimento que deixa o alvo seriamente envenenado. Seu dano venenoso aumenta a cada turno."}, "confusion": {"name": "Confusion", "effect": "O alvo é atingido por uma força telecinética fraca. <PERSON><PERSON> também pode deixar o alvo confuso."}, "psychic": {"name": "Psychic", "effect": "O alvo é atingido por uma força telecinética poderosa. <PERSON><PERSON> também pode diminuir a Defesa Especial do alvo."}, "hypnosis": {"name": "Hypnosis", "effect": "O usuário implanta uma sugestão hipnótica para fazer o alvo cair em um sono profundo."}, "meditate": {"name": "Meditate", "effect": "O usuário medita para despertar o poder profundo do seu corpo para aumentar seu Ataque."}, "agility": {"name": "Agility", "effect": "O usuário relaxa o corpo para se mover rapidamente. Isso aumenta bruscamente sua Velocidade."}, "quickAttack": {"name": "Quick Attack", "effect": "O usuário ataca o alvo em uma velocidade que o torna quase invisível. Esse movimento tem prioridade."}, "rage": {"name": "Rage", "effect": "Enquanto este movimento estiver em uso, o poder da ira aumenta o Ataque toda vez que o usuário for atingido em batalha."}, "teleport": {"name": "Teleport", "effect": "Use para fugir de qualquer Pokémon selvagem."}, "nightShade": {"name": "Night Shade", "effect": "O usuário faz com que o alvo veja uma miragem assustadora. Isso causa dano igual ao nível do usuário."}, "mimic": {"name": "Mimic", "effect": "O usuário copia o último movimento do alvo. O movimento pode ser usado durante a batalha até que o Pokémon seja trocado."}, "screech": {"name": "Screech", "effect": "Um grito estridente que reduz duramente o atributo de Defesa do alvo."}, "doubleTeam": {"name": "Double Team", "effect": "Movendo-se rapidamente, o usuário faz cópias ilusórias para aumentar sua Evasão."}, "recover": {"name": "Recover", "effect": "Restaurando suas células, o usuário restaura metade do seu máximo de PS."}, "harden": {"name": "<PERSON>en", "effect": "O usuário enrijece todos os músculos do seu corpo para aumentar seu atributo de Defesa."}, "minimize": {"name": "Minimize", "effect": "O usuário comprime seu corpo para se parecer menor, o que aumenta bruscamente sua Evasão."}, "smokescreen": {"name": "Smokescreen", "effect": "O usuário lança uma nuvem obscura de fumaça ou tinta. Isso diminui a Precisão do alvo."}, "confuseRay": {"name": "Confuse Ray", "effect": "O alvo é exposto a um raio sinistro que leva à confusão."}, "withdraw": {"name": "Withdraw", "effect": "O usuário retrai seu corpo para dentro de seu casco duro, aumentando o seu atributo de Defesa."}, "defenseCurl": {"name": "Defense Curl", "effect": "O usuário se enrola para esconder os pontos fracos e aumentar seu atributo de Defesa."}, "barrier": {"name": "Barrier", "effect": "O usuário ergue uma barreira robusta que aumenta bruscamente a sua Defesa."}, "lightScreen": {"name": "Light Screen", "effect": "Uma incrível parede de luz é erguida para reduzir o dano de ataques especiais por cinco turnos."}, "haze": {"name": "Haze", "effect": "O usuário cria uma névoa que elimina todas as alterações de atributos de todos os Pokémon em batalha."}, "reflect": {"name": "Reflect", "effect": "Uma incrível parede de luz é erguida para reduzir o dano de ataques físicos por cinco turnos."}, "focusEnergy": {"name": "Focus Energy", "effect": "O usuário respira fundo e concentra-se para que golpes críticos ocorram mais facilmente."}, "bide": {"name": "Bide", "effect": "O usuário resiste a ataques por dois turnos, e revida causando o dobro do dano recebido."}, "metronome": {"name": "Metronome", "effect": "O usuário balança um dedo e estimula seu cérebro para usar aleatoriamente quase qualquer movimento."}, "mirrorMove": {"name": "Mirror Move", "effect": "O usuário contra-ataca o alvo imitando seu último movimento utilizado."}, "selfDestruct": {"name": "Self-Destruct", "effect": "O usuário ataca tudo ao seu redor causando uma explosão. O usuário desmaia por usar esse golpe."}, "eggBomb": {"name": "Egg Bomb", "effect": "Um ovo grande é arremessado contra o alvo com força máxima para causar dano."}, "lick": {"name": "Lick", "effect": "O alvo é lambido com uma língua comprida, causando dano. <PERSON><PERSON> também pode deixar o alvo paralisado."}, "smog": {"name": "Smog", "effect": "O alvo é atacado com uma descarga de gases poluentes. <PERSON><PERSON> tamb<PERSON>m pode envenenar o alvo."}, "sludge": {"name": "Sludge", "effect": "Lodo insalubre é no arremessado no alvo. <PERSON><PERSON> também pode causar envenenamento."}, "boneClub": {"name": "Bone Club", "effect": "O usuário golpeia o alvo com um osso. <PERSON><PERSON> também pode o fazer o alvo hesitar."}, "fireBlast": {"name": "Fire Blast", "effect": "O alvo é atacado com uma intensa explosão de fogo consumidor. <PERSON><PERSON> também pode deixar o alvo com uma queimadura."}, "waterfall": {"name": "Waterfall", "effect": "O usuário investe no alvo e pode fazê-lo hesitar."}, "clamp": {"name": "C<PERSON>", "effect": "O alvo é apertado e imprensado pela concha densa e robusta do usuário por quatro ou cinco turnos."}, "swift": {"name": "Swift", "effect": "Raios em formato de estrela são disparados no Pokémon adversário. Esse ataque nunca erra."}, "skullBash": {"name": "<PERSON> Bash", "effect": "O usuário retrai sua cabeça para aumentar a Defesa no primeiro turno e depois se choca com o alvo no próximo turno."}, "spikeCannon": {"name": "<PERSON>", "effect": "Espinhos afiados são lançados no alvo em rápida sucessão. Eles acertam de duas a cinco vezes seguidas."}, "constrict": {"name": "Constrict", "effect": "O alvo é atacado com longos e sorrateiros tentáculos ou vinhas. <PERSON><PERSON> também pode diminuir a Velocidade do alvo."}, "amnesia": {"name": "Amnesia", "effect": "O usuário esvazia sua mente para esquecer suas preocupações. Aumenta bruscamente a Defesa Especial."}, "kinesis": {"name": "<PERSON><PERSON><PERSON>", "effect": "O usuário distrai o alvo entortando uma colher. <PERSON><PERSON> diminui a Precisão do alvo."}, "softBoiled": {"name": "Soft-Boiled", "effect": "O usuário restaura os próprios PS pela metade dos seus PS máximos."}, "highJumpKick": {"name": "High Jump Kick", "effect": "O alvo é atacado com uma joelhada de um pulo. Caso erre, o usuário se machuca."}, "glare": {"name": "<PERSON><PERSON><PERSON>", "effect": "O usuário intimida o alvo com o padrão em sua barriga para causar paralisia."}, "dreamEater": {"name": "Dream Eater", "effect": "O usuário se alimenta dos sonhos de um alvo adormecido. Ele absorve metade do dano causado para curar seus PS."}, "poisonGas": {"name": "Poison Gas", "effect": "Uma nuvem de gás venenoso é assoprada no rosto do Pokémon adversário. <PERSON><PERSON> pode envenenar os alvos."}, "barrage": {"name": "Barrage", "effect": "Objetos redondos são lançados no alvo para acertar de duas a cinco vezes seguidas."}, "leechLife": {"name": "Leech Life", "effect": "O usuário drena o sangue do alvo. Os PS do usuário são restaurados pela metade do dano recebido pelo alvo."}, "lovelyKiss": {"name": "Lovely Kiss", "effect": "Com uma face assust<PERSON><PERSON>, o usuário tenta beijar o alvo à força. Se conseguir, o alvo cai no sono."}, "skyAttack": {"name": "Sky Attack", "effect": "Um movimento de dois turnos onde golpes críticos ocorrem mais facilmente. Também pode fazer o alvo hesitar."}, "transform": {"name": "Transform", "effect": "O usuário transforma-se em uma cópia do alvo, conseguindo os mesmos movimentos do adversário."}, "bubble": {"name": "Bubble", "effect": "Um jato de incontáveis bolhas é disparado no Pokémon adversário. <PERSON><PERSON> também pode diminuir a velocidade do alvo."}, "dizzyPunch": {"name": "<PERSON><PERSON>", "effect": "O alvo é atingido com socos dados ritmicamente. <PERSON><PERSON> também pode deixar alvo confuso."}, "spore": {"name": "Spore", "effect": "O usuário espalha rajadas de esporos que induzem sono ao alvo."}, "flash": {"name": "Flash", "effect": "O usuário pisca uma luz brilhante que reduz a Precisão do alvo."}, "psywave": {"name": "Psywave", "effect": "O alvo é atacado com uma estranha onda psíquica. O ataque varia de intensidade."}, "splash": {"name": "Splash", "effect": "O usuário apenas debate-se no chão e espirra água ao seu redor sem efeito algum…"}, "acidArmor": {"name": "Acid <PERSON>", "effect": "O usuário altera sua estrutura celular para se liquefazer, aumentando bruscamente o seu atributo de Defesa."}, "crabhammer": {"name": "<PERSON>rab<PERSON>", "effect": "O alvo é martelado com uma grande pinça. Golpes críticos acertam mais facilmente."}, "explosion": {"name": "Explosion", "effect": "O usuário ataca tudo o que estiver à sua volta causando uma tremenda explosão. O usuário desmaia ao usar esse movimento."}, "furySwipes": {"name": "<PERSON>", "effect": "O alvo é atacado com garras afiadas ou foices rapidamente, de duas a cinco vezes seguidas."}, "bonemerang": {"name": "Bonemerang", "effect": "O usuário arremessa o osso que segura. O osso gira, atingindo o alvo duas vezes, indo e voltando."}, "rest": {"name": "Rest", "effect": "O usuário dorme por dois turnos. Isso restaura completamente os PS do usuário e cura quaisquer condições negativas."}, "rockSlide": {"name": "Rock Slide", "effect": "Pedras grandes são arremessadas no Pokémon oponente para causar dano. <PERSON><PERSON> também pode fazer o Pokémon oponente hesitar."}, "hyperFang": {"name": "Hyper Fang", "effect": "O usuário morde o alvo com força, usando as suas afiadas presas frontais. <PERSON><PERSON> também pode fazer o alvo hesitar."}, "sharpen": {"name": "Sharpen", "effect": "O usuário abaixa o número de polígonos, ficando mais pont<PERSON>. Isso aumenta o seu atributo de Ataque."}, "conversion": {"name": "Conversion", "effect": "O usuário muda seu tipo para o mesmo tipo do movimento no topo da lista dos movimentos que conhece no momento."}, "triAttack": {"name": "Tri Attack", "effect": "O usuário golpeia com um ataque de três raios simultâneos. Também pode queimar, congelar ou paralisar o alvo."}, "superFang": {"name": "Super Fang", "effect": "O usuário mastiga com força o alvo usando suas afiadas presas frontais. Isso corta os PS do alvo pela metade."}, "slash": {"name": "Slash", "effect": "O alvo é atacado com um açoite de garras ou lâminas. Golpes críticos ocorrem mais facilmente."}, "substitute": {"name": "Substitute", "effect": "O usuário faz uma cópia de si mesmo usando parte de seus PS. A cópia serve como uma isca para o usuário."}, "struggle": {"name": "<PERSON><PERSON><PERSON>", "effect": "Um ataque usado em desespero, apenas se o usuário não tiver PP. Isso também causa um pouco de dano no usuário."}, "sketch": {"name": "Sketch", "effect": "Permite que o usuário aprenda permanentemente o último movimento usado pelo alvo. Assim que for usado, o Esboço desaparece."}, "tripleKick": {"name": "Triple Kick", "effect": "Um ataque de três chutes consecutivos que se fortalece a cada golpe acertado."}, "thief": {"name": "<PERSON>hief", "effect": "O usuário ataca e tenta roubar o alvo simultaneamente. Este movimento tem 30% de chance de roubar um dos itens que o alvo está segurando."}, "spiderWeb": {"name": "Spider Web", "effect": "O usuário enlaça o alvo com uma teia fina e grudenta, para que ele não possa fugir da batalha."}, "mindReader": {"name": "Mind Reader", "effect": "O usuário pressente os movimentos do alvo com sua mente para ter certeza que o seu próximo ataque não o erre."}, "nightmare": {"name": "Nightmare", "effect": "Um alvo que dorme terá um pesadelo que causará dano a cada turno."}, "flameWheel": {"name": "Flame Wheel", "effect": "O usuário se envolve em fogo e dispara em direção ao alvo. <PERSON><PERSON> também pode causar queimaduras no oponente."}, "snore": {"name": "<PERSON><PERSON><PERSON>", "effect": "Um ataque que só pode ser usado se o usuário estiver dormindo. O barulho alto pode fazer o alvo hesitar."}, "curse": {"name": "Curse", "effect": "Se usado por um tipo Fantasma, o usuário perde PS igual à metade dos seus PS máximos para amaldiçoar o alvo, causando dano a cada turno. <PERSON><PERSON><PERSON> contr<PERSON><PERSON>, o usuário reduz seu atributo de Velocidade, mas aumenta seus atributos de Ataque e Defesa."}, "flail": {"name": "<PERSON><PERSON>l", "effect": "O usuário agita os membros sem rumo para atacar. Quanto menores forem os PS do usuário, melhor será o movimento."}, "conversion2": {"name": "Conversion 2", "effect": "O usuário muda a própria tipagem para se fazer resistente ao tipo do último ataque usado pelo oponente."}, "aeroblast": {"name": "Aeroblast", "effect": "Um vortex de vento é atirado em direção ao alvo para causar dano. Golpes críticos ocorrem mais facilmente."}, "cottonSpore": {"name": "Cotton Spore", "effect": "O usuário solta esporos de algodão que grudam no Pokémon adversário. Isso prejudica bruscamente a Velocidade do oponente."}, "reversal": {"name": "Reversal", "effect": "Um ataque total que fica mais forte quanto menos PS o usuário possuir."}, "spite": {"name": "Spite", "effect": "O usuário libera todo o seu rancor no último movimento usado pelo oponente, cortando 4 PP do mesmo."}, "powderSnow": {"name": "Powder Snow", "effect": "O usuário ataca com uma brisa congelante de Neve em Pó. <PERSON><PERSON> talvez possa congelar o Pokémon adversário."}, "protect": {"name": "Protect", "effect": "Permite que o usuário desvie de todos os ataques. A sua chance de falhar aumenta, caso seja usado em sucessão."}, "machPunch": {"name": "<PERSON><PERSON> Punch", "effect": "O usuário soca numa velocidade incompreensível. Esse movimento tem prioridade."}, "scaryFace": {"name": "Scary <PERSON>", "effect": "O usuário assusta o alvo com uma cara assustadora para prejudicar duramente a velocidade do oponente."}, "feintAttack": {"name": "Feint Attack", "effect": "O usuário se aproxima do alvo amigavelmente, então ataca com um soco inesperado. Esse ataque nunca erra."}, "sweetKiss": {"name": "Sweet Kiss", "effect": "O usuário beija o alvo com uma fofura doce e angelical, causando confusão."}, "bellyDrum": {"name": "Belly Drum", "effect": "O usuário maximiza seu Ataque em troca de PS igual à metade do seu PS máximo."}, "sludgeBomb": {"name": "Sludge Bomb", "effect": "Lodo insalubre é no arremessado no alvo. <PERSON><PERSON> também pode causar envenenamento."}, "mudSlap": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect": "O usuário arremessa lama no rosto do adversário para causar dano e prejudicar sua precisão."}, "octazooka": {"name": "Octazooka", "effect": "O usuário ataca jogando tinta no rosto ou nos olhos do alvo. <PERSON><PERSON> pode prejudicar a Precisão do alvo."}, "spikes": {"name": "Spikes", "effect": "O usuário lança armadilhas de espinhos nos pés da equipe adversária. As armadilhas ferem os Pokémon que são trocados em batalha."}, "zapCannon": {"name": "Zap Cannon", "effect": "O usuário atira uma explosão elétrica como um canhão para infligir dano e causar paralisia."}, "foresight": {"name": "Foresight", "effect": "Permite que um alvo do tipo Fantasma seja atingido por ataques do tipo Normal e Lutador. <PERSON><PERSON> também permite que um alvo evasivo seja acertado."}, "destinyBond": {"name": "Destiny Bond", "effect": "Quando esse movimento é usado, caso o usu<PERSON>rio desma<PERSON>, o Pokémon que acertou o nocaute também desmaiará. A chance de falhar aumenta se usado em sucessão."}, "perishSong": {"name": "Perish Song", "effect": "Qualquer Pokémon que ouvir essa Canção desmaiará em três turnos, a não ser que ele seja retirado da batalha."}, "icyWind": {"name": "<PERSON><PERSON>", "effect": "O usuário ataca com uma rajada de ar arrepiante. <PERSON><PERSON> também prejudica a Velocidade do Pokémon adversário."}, "detect": {"name": "Detect", "effect": "Permite que o usuário desvie de todos os ataques. A sua chance de falhar aumenta, caso seja usado em sucessão."}, "boneRush": {"name": "<PERSON>", "effect": "O usuário atinge o alvo com um osso duro de duas a cinco vezes seguidas."}, "lockOn": {"name": "Lock-On", "effect": "O usuário foca sua mira no alvo. <PERSON><PERSON> garante que o próximo ataque não erre o alvo."}, "outrage": {"name": "Outrage", "effect": "O usuário fica furioso e ataca com violência de dois a três turnos. O usuário então se torna confuso."}, "sandstorm": {"name": "Sandstorm", "effect": "Uma tempestade de areia é invocada durante 5 turnos para ferir todos os combatentes, exceto os tipos Pedra, Terra e Aço. Isso aumenta a Defesa Especial dos tipo Pedra."}, "gigaDrain": {"name": "Giga Drain", "effect": "Um ataque que drena nutrientes. O usuário recupera PS pela metade do dano infligido ao alvo."}, "endure": {"name": "Endure", "effect": "O usuário resiste a qualquer ataque com pelo menos 1 PS. A chance de falhar aumenta caso seja usado em sucessão."}, "charm": {"name": "Charm", "effect": "O usuário contempla o alvo com um olhar charmoso, fazendo-o ficar menos atento. Isso prejudica duramente o Ataque do oponente."}, "rollout": {"name": "Rollout", "effect": "O usuário rola continuamente em direção ao alvo por cinco turnos. O ataque fica mais forte a cada acerto."}, "falseSwipe": {"name": "False Swipe", "effect": "Um ataque moderado que previne que o alvo desmaie. O alvo é deixado com pelo menos 1 de PS."}, "swagger": {"name": "Swagger", "effect": "O usuário enfurece e confunde o alvo. Entretanto, isso também aumenta bruscamente o Ataque do alvo."}, "milkDrink": {"name": "Milk Drink", "effect": "O usuário restaura os próprios PS pela metade dos seus PS máximos."}, "spark": {"name": "Spark", "effect": "O usuário direciona uma investida carregada com eletricidade no alvo. <PERSON><PERSON> pode paralisar o alvo."}, "furyCutter": {"name": "Fury Cutter", "effect": "O alvo é cortado com foices ou garras. Esse ataque se torna mais poderoso se usado em sucessão."}, "steelWing": {"name": "Steel Wing", "effect": "O alvo é atingido com asas de aço. Isso também pode aumentar a Defesa do usuário."}, "meanLook": {"name": "Mean Look", "effect": "O usuário encara o alvo com um olhar sombrio e opressor. O alvo se torna incapaz de fugir."}, "attract": {"name": "Attract", "effect": "Caso o adversário seja do gênero oposto ao usuário, o alvo se apaixona e se torna menos suscetível a atacar."}, "sleepTalk": {"name": "Sleep Talk", "effect": "Enquanto está dormindo, o usuário usa aleatoriamente um dos movimentos que tem conhecimento."}, "healBell": {"name": "<PERSON><PERSON>", "effect": "O usuário toca um sino calmante para curar condições de estados de todos os Pokémon aliados na equipe."}, "return": {"name": "Return", "effect": "Um ataque poderoso que fica mais poderoso à medida que o usuário gosta de seu Treinador."}, "present": {"name": "Present", "effect": "O usuário ataca o alvo entregando um presente com uma armadilha oculta. Entretanto, às vezes cura os PS do alvo."}, "frustration": {"name": "Frustration", "effect": "Um ataque poderoso que fica mais poderoso à medida que o usuário desgosta de seu Treinador."}, "safeguard": {"name": "Safeguard", "effect": "O usuário cria um campo protetor que previne condições de estado por cinco turnos."}, "painSplit": {"name": "Pain Split", "effect": "O usuário adiciona os próprios PS aos PS do alvo, então compartilha igualmente os PS combinados com o alvo."}, "sacredFire": {"name": "Sacred Fire", "effect": "O usuário é arrasado com uma rajada de fogo místico de grande intensidade. <PERSON><PERSON> pode deixar o alvo queimado."}, "magnitude": {"name": "Magnitude", "effect": "O usuário ataca tudo ao seu redor com um grande tremor. Seu poder varia."}, "dynamicPunch": {"name": "Dynamic Punch", "effect": "O usuário soca o alvo com sua força totalmente concentrada. Em caso de acerto, confundirá o alvo."}, "megahorn": {"name": "Megahorn", "effect": "Usando seu impressionante chifre resistente, o usuário golpeia o alvo sem trégua."}, "dragonBreath": {"name": "Dragon Breath", "effect": "O usuário sopra uma poderosa rajada que causa dano. <PERSON><PERSON> também pode paralisar o alvo."}, "batonPass": {"name": "Baton Pass", "effect": "O usuário troca de lugar com um Pokémon da equipe em espera e passa para ele quaisquer mudanças de atributos."}, "encore": {"name": "Encore", "effect": "O usuário enaltece o alvo para que ele continue usando o movimento que ele usou por último durante três turnos."}, "pursuit": {"name": "Pursuit", "effect": "Um ataque que causa o dobro do dano caso seja usado em um alvo que esteja sendo trocado para fora da batalha."}, "rapidSpin": {"name": "Rapid Spin", "effect": "Um ataque giratório que pode anular movimentos como Enlaçar, <PERSON><PERSON><PERSON><PERSON><PERSON>, Sanguessuga e Espinhos."}, "sweetScent": {"name": "Sweet Scent", "effect": "Um aroma doce que prejudica duramente a Evasão do Pokémon adversário."}, "ironTail": {"name": "Iron Tail", "effect": "O alvo é esmagado com uma cauda dura como aço. <PERSON><PERSON> tamb<PERSON>m pode prejudicar a Defesa do alvo."}, "metalClaw": {"name": "Metal Claw", "effect": "O alvo é cortado com garras de metal. Isso também pode aumentar o Ataque do usuário."}, "vitalThrow": {"name": "<PERSON><PERSON>", "effect": "O usuário sempre ataca por último. Em troca, esse arremesso nunca erra."}, "morningSun": {"name": "Morning Sun", "effect": "O usuário restaura os próprios PS. A quantidade de PS recuperada varia conforme o clima."}, "synthesis": {"name": "Synthesis", "effect": "O usuário restaura os próprios PS. A quantidade de PS recuperada varia conforme o clima."}, "moonlight": {"name": "Moonlight", "effect": "O usuário restaura os próprios PS. A quantidade de PS recuperada varia conforme o clima."}, "hiddenPower": {"name": "Hidden Power", "effect": "Um ataque único que varia em tipo dependendo do Pokémon que está utilizando."}, "crossChop": {"name": "Cross Chop", "effect": "O usuário dá um golpe duplo com seus antebraços cruzados. Golpes críticos ocorrem mais facilmente."}, "twister": {"name": "<PERSON><PERSON><PERSON>", "effect": "O usuário rapidamente cria um tornado vicioso para rasgar os Pokémon adversários. Isso pode fazê-los hesitar."}, "rainDance": {"name": "Rain Dance", "effect": "O usuário invoca uma chuva intensa que continua caindo por cinco turnos, fortalecendo os movimentos do tipo Água. Também enfraquece os movimentos do tipo fogo."}, "sunnyDay": {"name": "Sunny Day", "effect": "O usuário intensifica o sol por cinco turnos, fortalecendo os ataques do tipo Fogo. Também enfraquece os movimentos do tipo água."}, "crunch": {"name": "Crunch", "effect": "O usuário mastiga o alvo com presas afiadas. Is<PERSON> também pode prejudicar a Defesa do alvo."}, "mirrorCoat": {"name": "Mirror Coat", "effect": "Um movimento de retaliação que contra-ataca qualquer ataque especial, infligindo o dobro do dano recebido."}, "psychUp": {"name": "Psych Up", "effect": "O usuário hipnotiza a si mesmo para copiar qualquer mudança de atributo feita pelo alvo."}, "extremeSpeed": {"name": "Extreme Speed", "effect": "O usuário ataca o alvo numa velocidade invisível ao olho nu. Esse movimento tem prioridade."}, "ancientPower": {"name": "Ancient Power", "effect": "O usuário ataca com um poder pré-histórico. <PERSON><PERSON> ta<PERSON>m pode fortalecer todos os atributos do usuário de uma vez."}, "shadowBall": {"name": "Shadow Ball", "effect": "O usuário arremessa uma esfera sombria no alvo. <PERSON><PERSON> tamb<PERSON>m pode prejudicar a Defesa Especial do alvo."}, "futureSight": {"name": "Future Sight", "effect": "<PERSON>is turnos após esse movimento ser usado, uma grande quantidade de energia psíquica atinge o alvo."}, "rockSmash": {"name": "Rock Smash", "effect": "O usuário ataca com um soco. Isso também pode prejudicar o atributo de Defesa do alvo."}, "whirlpool": {"name": "Whirlpool", "effect": "O usuário prende o alvo num violento redemoinho por quatro ou cinco turnos."}, "beatUp": {"name": "Beat Up", "effect": "O usuário reúne todos os Pokémon da equipe para atacar o alvo. Quanto maior o número de Pokémon na equipe, maior será o número de ataques."}, "fakeOut": {"name": "Fake Out", "effect": "Um ataque que acerta primeiro e faz o alvo hesitar. Só funciona no primeiro turno do usuário em batalha."}, "uproar": {"name": "Uproar", "effect": "O usuário ataca gritando por três turnos. Durante esse tempo, ninguém pode cair no sono."}, "stockpile": {"name": "Stockpile", "effect": "O usuário carrega seu poder e fortalece a Defesa e a Defesa Especial. O movimento pode ser usado três vezes."}, "spitUp": {"name": "Spit Up", "effect": "O poder carregado usando o movimento Estocagem é liberado de uma só vez em um ataque. Quanto mais poder é armazenado, maior a força do movimento."}, "swallow": {"name": "S<PERSON>ow", "effect": "O poder carregado usando o movimento Estocagem é absorvido pelo usuário para curar PS. Quanto mais poder for armazenado, mais PS serão recuperados."}, "heatWave": {"name": "Heat Wave", "effect": "O usuário ataca exalando um sopro ardente nos Pokémon adversários. <PERSON><PERSON> também pode deixar esses Pokémon queimados."}, "hail": {"name": "<PERSON>l", "effect": "O usuário invoca uma tempestade de granizo durante cinco turnos. <PERSON><PERSON> fere todos os Pokémon, exceto os tipo Gelo."}, "torment": {"name": "Torment", "effect": "O usuário atormenta o alvo, fazendo-o incapaz de usar o mesmo movimento duas vezes seguidas."}, "flatter": {"name": "Flatter", "effect": "Bajulação é usada para confundir o oponente. Entretanto, isso também fortalece o Ataque Especial do alvo."}, "willOWisp": {"name": "Will-O-Wisp", "effect": "O usuário atira uma sinistra chama azulada no alvo para causar uma queimadura."}, "memento": {"name": "Memento", "effect": "O usuário desmaia quando usa esse movimento. Em troca, prejudica duramente o Ataque e o Ataque Especial do alvo."}, "facade": {"name": "Facade", "effect": "Um ataque que dobra de poder caso o usuário esteja envenenado, queimado ou paralisado."}, "focusPunch": {"name": "Focus Punch", "effect": "O usuário foca sua mente antes de dar um soco. Esse ataque falhará caso o usuário seja atingido antes de executá-lo."}, "smellingSalts": {"name": "Smelling Salts", "effect": "Esse ataque causa o dobro do dano em um alvo paralisado. Entretanto, isso também cura a paralisia do alvo."}, "followMe": {"name": "Follow Me", "effect": "O usuário chama a atenção para si mesmo, fazendo todos os alvos mirarem apenas nele."}, "naturePower": {"name": "Nature Power", "effect": "Um ataque que faz uso do poder da natureza. Seus efeitos variam dependendo do ambiente ao redor do usuário."}, "charge": {"name": "Charge", "effect": "O usuário fortalece o poder do golpe do tipo Elétrico usado no próximo turno. <PERSON>so também fortalece a Defesa Especial do usuário."}, "taunt": {"name": "<PERSON><PERSON>", "effect": "O alvo é provocado e fica em fúria, fazendo-o usar apenas movimentos de ataque por três turnos."}, "helpingHand": {"name": "Helping Hand", "effect": "O usuário auxilia um aliado, fortalecendo o poder do ataque desse aliado."}, "trick": {"name": "Trick", "effect": "O usuário pega o alvo de surpresa e faz uma troca de itens com o alvo."}, "rolePlay": {"name": "Role Play", "effect": "O usuário imita o alvo completamente, copiando a Habilidade natural do alvo."}, "wish": {"name": "Wish", "effect": "Um turno após esse movimento ter sido usado, os PS do usuário ou de seu substituinte são restaurados pela metade dos PS máximos do usuário."}, "assist": {"name": "Assist", "effect": "O usuário na pressa usa aleatoriamente um dos movimentos conhecidos pelos outros Pokémon na equipe."}, "ingrain": {"name": "Ingrain", "effect": "O usuário planta suas raízes para curar seus PS por turno. Devido ao enraizamento, ele não pode sair da batalha."}, "superpower": {"name": "Superpower", "effect": "O usuário ataca o alvo com grande poder. Entretanto, isso também prejudica o Ataque e Defesa do usuário."}, "magicCoat": {"name": "Magic Coat", "effect": "Uma barreira que reflete de volta ao alvo movimentos como Semente Drenante e movimentos que reduzem atributos."}, "recycle": {"name": "Recycle", "effect": "O usuário recicla um item segurado que já foi usado em batalha para que possa ser usado de novo."}, "revenge": {"name": "Revenge", "effect": "Um movimento atacante que inflige o dobro do dano se o usuário foi ferido pelo adversário no mesmo turno."}, "brickBreak": {"name": "Brick Break", "effect": "O usuário ataca com um corte veloz. <PERSON><PERSON> também quebra barreiras como Tela de Luz e Refletir."}, "yawn": {"name": "Yawn", "effect": "O usuário dá um grande e preguiçoso bocejo que acalma o alvo, fazendo-o cair no sono no próximo turno."}, "knockOff": {"name": "Knock Off", "effect": "O usuário dá um tapa no item segurado pelo alvo e esse item não poderá mais ser usado naquela batalha. Caso possua um item, o alvo receberá mais dano."}, "endeavor": {"name": "Endeavor", "effect": "Um movimento de ataque que corta os PS do alvo para que se equalize aos PS do usuário."}, "eruption": {"name": "Eruption", "effect": "O usuário ataca o Pokémon adversário com uma fúria explosiva. Quanto menor for os PS do usuário, menor será o poder do movimento."}, "skillSwap": {"name": "<PERSON><PERSON>", "effect": "O usuário utiliza seu poder psíquico para trocar de Habilidade com o alvo."}, "imprison": {"name": "Imprison", "effect": "Se os Pokémon adversários conhecerem algum movimento também conhecido pelo usuário, eles não poderão usá-lo."}, "refresh": {"name": "Refresh", "effect": "O usuário descansa para curar a si mesmo de envenenamentos, queimaduras ou paralisias."}, "grudge": {"name": "Grudge", "effect": "Se o usuário des<PERSON>, o rancor do usuário vai esgotar completamente os PP do movimento que o nocauteou."}, "snatch": {"name": "Snatch", "effect": "O usuário rouba o efeito de qualquer tentativa de usar um movimento de cura ou mudança de atributo."}, "secretPower": {"name": "Secret Power", "effect": "Os efeitos adicionais deste movimento variam dependendo do ambiente ao redor do usuário."}, "dive": {"name": "Dive", "effect": "Mergulhando no primeiro turno, o usuário emerge e ataca no próximo turno."}, "armThrust": {"name": "Arm Thrust", "effect": "O usuário solta uma sequência de golpes braçais com as palmas abertas, de duas a cinco vezes seguidas."}, "camouflage": {"name": "Camouflage", "effect": "O tipo do usuário é mudado dependendo do ambiente ao seu redor, como na margem da água, rodeado por grama ou dentro de uma caverna."}, "tailGlow": {"name": "<PERSON><PERSON>", "effect": "O usuário direciona seu olhar à luzes piscantes para focar sua mente, aumentando drasticamente o seu Ataque Especial."}, "lusterPurge": {"name": "Luster Purge", "effect": "O usuário libera uma explosão de luz letal. <PERSON><PERSON> pode prejudicar a Defesa Especial do alvo."}, "mistBall": {"name": "Mist Ball", "effect": "Um amalgamado de penas nevoentas envolvem e ferem o alvo. <PERSON><PERSON> pode prejudicar o Ataque Especial do alvo."}, "featherDance": {"name": "Feather Dance", "effect": "O usuário cobre o corpo do alvo com uma grande massa de penas que prejudicam duramente o Ataque do alvo."}, "teeterDance": {"name": "Teeter Dance", "effect": "O usuário performa uma dança desajeitada que confunde os Pokémon ao seu redor."}, "blazeKick": {"name": "Blaze Kick", "effect": "O usuário lança um chute que acerta golpes críticos com mais facilidade. <PERSON><PERSON> também pode deixar o alvo queimado."}, "mudSport": {"name": "Mud Sport", "effect": "O usuário chuta lama ao redor do campo de batalha. <PERSON><PERSON> enfraquece os golpes do tipo Elétrico por cinco turnos."}, "iceBall": {"name": "Ice Ball", "effect": "O usuário ataca continuamente por cinco turnos. O ataque se torna mais forte cada vez que acerta."}, "needleArm": {"name": "Needle Arm", "effect": "O usuário ataca selvagemente balançando seus braços pontiagudos. Isso pode fazer o alvo hesitar."}, "slackOff": {"name": "Slack Off", "effect": "O usuário relaxa, restaurando os próprios PS pela metade dos seus PS máximos."}, "hyperVoice": {"name": "Hyper Voice", "effect": "O usuário libera um horrível grito estridente com o poder de infligir dano."}, "poisonFang": {"name": "Poison Fang", "effect": "O usuário morde o alvo com suas presas tóxicas. <PERSON><PERSON> pode envenenar seriamente o alvo."}, "crushClaw": {"name": "Crush Claw", "effect": "O usuário retalha o alvo com garras duras e afiadas. <PERSON><PERSON> pode prejudicar a Defesa do alvo."}, "blastBurn": {"name": "Blast Burn", "effect": "O alvo é arrasado por uma ardente explosão. O usuário não pode se mover no próximo turno."}, "hydroCannon": {"name": "Hydro Cannon", "effect": "O alvo é acertado por uma explosão aquática. O usuário não pode se mover no próximo turno."}, "meteorMash": {"name": "Meteor Mash", "effect": "O alvo é acertado por um soco forte disparado como um meteoro. <PERSON>so pode fortalecer o Ataque do usuário."}, "astonish": {"name": "Astonish", "effect": "O usuário ataca o alvo enquanto grita de forma alarmante. <PERSON><PERSON> pode fazer o alvo hesitar."}, "weatherBall": {"name": "Weather Ball", "effect": "Um ataque que varia de poder e tipo dependendo do clima."}, "aromatherapy": {"name": "Aromatherapy", "effect": "O usuário lança uma fragrância calmante que cura todos os estados negativos afetando a equipe do usuário."}, "fakeTears": {"name": "Fake Tears", "effect": "O usuário finge chorar para perturbar o alvo, duramente prejudicando a sua Defesa Especial."}, "airCutter": {"name": "Air Cutter", "effect": "O usuário lança uma rajada de vento afiado para cortar seus oponentes. Golpes críticos ocorrem mais facilmente."}, "overheat": {"name": "Overheat", "effect": "O usuário ataca o alvo com seu poder máximo; contudo, o efeito colateral diminui o Ataque Especial do usuário."}, "odorSleuth": {"name": "<PERSON><PERSON>", "effect": "Permite que um alvo do tipo Fantasma seja atingido por ataques do tipo Normal e Lutador. <PERSON><PERSON> também permite que um alvo evasivo seja acertado."}, "rockTomb": {"name": "Rock Tomb", "effect": "Rochas são arremessadas no alvo. Isso também diminui a Velocidade do alvo, impedindo que se movimente."}, "silverWind": {"name": "Silver Wind", "effect": "O alvo é atacado com escamas pulverulentas sopradas pelo vento. <PERSON>so pode aumentar todos os atributos do usuário."}, "metalSound": {"name": "Metal Sound", "effect": "Um som horrível que lembra metal sendo raspado, isso prejudica duramente a Defesa Especial do alvo."}, "grassWhistle": {"name": "Grass Whistle", "effect": "O usuário toca uma agradável melodia que acalma o alvo, fazendo-o entrar em sono profundo."}, "tickle": {"name": "Tickle", "effect": "O usuário faz cócegas no alvo, o fazendo rir, reduzindo seus atributos de Ataque e Defesa."}, "cosmicPower": {"name": "Cosmic Power", "effect": "O usuário absorve energia mística do espaço para aumentar sua Defesa e Defesa Especial."}, "waterSpout": {"name": "Water Spout", "effect": "O usuário jorra água para ferir os Pokémon oponentes. Quanto menos PS o usuário tiver, menor será o poder do movimento."}, "signalBeam": {"name": "Signal Beam", "effect": "O usuário ataca com um raio de luz sinistro. <PERSON><PERSON> tamb<PERSON>m pode confundir o alvo."}, "shadowPunch": {"name": "Shadow Punch", "effect": "O usuário dispara um soco dentre as sombras. Esse ataque nunca erra."}, "extrasensory": {"name": "Extrasensory", "effect": "O usuário ataca com um poder estranho e incompreensível. <PERSON><PERSON> também pode fazer o alvo hesitar."}, "skyUppercut": {"name": "Sky Uppercut", "effect": "O usuário ataca o alvo com um gancho de direita poderoso direcionado ao céu."}, "sandTomb": {"name": "Sand Tomb", "effect": "O usuário prende o alvo dentro de uma violenta tempestade de areia por quatro ou cinco turnos."}, "sheerCold": {"name": "<PERSON><PERSON>", "effect": "O alvo desmaia instantaneamente. É mais difícil de acertar se o usuário não for um tipo Gelo."}, "muddyWater": {"name": "Muddy Water", "effect": "O usuário ataca atirando água barrenta nos Pokémon adversários. <PERSON><PERSON> também pode diminuir a Precisão deles."}, "bulletSeed": {"name": "Bullet Seed", "effect": "O usuário atira sementes com grande força no alvo de duas a cinco vezes seguidas."}, "aerialAce": {"name": "Aerial Ace", "effect": "O usuário confunde o alvo com sua velocidade e então ataca. Esse ataque nunca erra."}, "icicleSpear": {"name": "<PERSON><PERSON><PERSON>", "effect": "O usuário arremessa lanças afiadas de gelo no alvo de duas a cinco vezes seguidas."}, "ironDefense": {"name": "Iron Defense", "effect": "O usuário endurece a superfície de seu corpo como aço, bruscamente fortalecendo sua Defesa."}, "block": {"name": "Block", "effect": "O usuário bloqueia o caminho do alvo com seus braços amplamente abertos para prevenir escapatória."}, "howl": {"name": "Howl", "effect": "O usuário uiva alto para fortalecer seu espirito, o que aumenta seu Ataque."}, "dragonClaw": {"name": "Dragon Claw", "effect": "O usuário corta o alvo com grandes garras afiadas."}, "frenzyPlant": {"name": "Frenzy Plant", "effect": "O usuário esmaga o alvo com uma enorme árvore. O usuário não pode se mover no próximo turno."}, "bulkUp": {"name": "Bulk Up", "effect": "O usuário flexiona os seus músculos para fortalecer seu corpo, aumentando os seus atributos de Ataque e Defesa."}, "bounce": {"name": "<PERSON><PERSON><PERSON>", "effect": "O usuário pula alto e então cai em cima do alvo no segundo turno. <PERSON><PERSON> também pode deixar o alvo com paralisia."}, "mudShot": {"name": "Mud Shot", "effect": "O usuário ataca arremessando uma bola de lama ao alvo. Isso também diminui a Velocidade do alvo."}, "poisonTail": {"name": "Poison Tail", "effect": "O usuário acerta o alvo com sua cauda. <PERSON><PERSON> também pode envenenar o alvo. Golpes críticos ocorrem mais facilmente."}, "covet": {"name": "Covet", "effect": "O usuário se aproxima do alvo de forma amigável e tenta roubá-lo. Este movimento tem 30% de chance de roubar um dos itens que o alvo está segurando."}, "voltTackle": {"name": "Volt Tackle", "effect": "O usuário eletrifica a si próprio e então ataca. <PERSON><PERSON> também fere muito o usuário. Pode deixar o alvo com paralisia."}, "magicalLeaf": {"name": "Magical Leaf", "effect": "O usuário espalha folhas peculiares que perseguem o alvo. Esse ataque nunca erra."}, "waterSport": {"name": "Water Sport", "effect": "O usuário encharca o campo de batalha. <PERSON><PERSON> enfraquece os movimentos do tipo Fogo por cinco turnos."}, "calmMind": {"name": "Calm Mind", "effect": "O usuário silenciosamente focaliza sua mente e acalma o seu espírito para aumentar ambos os atributos especiais."}, "leafBlade": {"name": "Leaf Blade", "effect": "O usuário empunha uma folha afiada como uma espada e ataca cortando o alvo. Golpes críticos acertam mais facilmente."}, "dragonDance": {"name": "Dragon Dance", "effect": "O usuário, energicament<PERSON>, performa uma dança mística e poderosa para aumentar seu Ataque e Velocidade."}, "rockBlast": {"name": "Rock Blast", "effect": "O usuário arremessa rochas duras no alvo. Duas a cinco rochas são lançadas em sequência."}, "shockWave": {"name": "Shock Wave", "effect": "O usuário atinge o alvo com um repentino ataque de eletricidade. Esse ataque nunca erra."}, "waterPulse": {"name": "Water Pulse", "effect": "O usuário ataca o alvo com uma pulsante explosão de água. <PERSON><PERSON>z isso confunda o alvo."}, "doomDesire": {"name": "Doom Desire", "effect": "Dois turnos após esse movimento ter sido usado, o usuário explode o alvo com um feixe de luz concentrado."}, "psychoBoost": {"name": "P<PERSON>cho <PERSON>", "effect": "O usuário ataca o alvo com poder máximo. O efeito colateral do ataque prejudica duramente o Ataque Especial do usuário."}, "roost": {"name": "Roost", "effect": "O usuário pousa e descansa seu corpo. <PERSON><PERSON> restaura os PS do usuário pela metade do seu máximo de PS."}, "gravity": {"name": "Gravity", "effect": "Permite que Pokémon do tipo Voador ou Pokémon com a Habilidade Levitação possam ser atingidos por golpes do tipo Terra. Golpes que envolvam voar ficam inutilizados."}, "miracleEye": {"name": "Miracle Eye", "effect": "Permite que um alvo tipo Sombrio seja atingido por ataques do tipo Psíquico. Isso também permite que um alvo evasivo possa ser atingido."}, "wakeUpSlap": {"name": "Wake-Up Slap", "effect": "Esse ataque causa muito dano em um alvo que estiver dormindo; entretanto, isso também acorda o alvo."}, "hammerArm": {"name": "Hammer Arm", "effect": "O usuário balança seus braços e atinge com seus fortes e pesados punhos. Isso diminui a Velocidade do usuário."}, "gyroBall": {"name": "Gyro <PERSON>", "effect": "O alvo é acertado com um giro em alta velocidade. Quanto mais lento for o usuário comparado ao alvo, maior será o poder do movimento."}, "healingWish": {"name": "Healing Wish", "effect": "O usuário desmaia. Em troca, o Pokémon que tomará seu lugar terá seus PS restaurados e condições negativas curadas."}, "brine": {"name": "<PERSON><PERSON>", "effect": "Se os PS do alvo estiverem pela metade ou menos, esse ataque terá o dobro do poder."}, "naturalGift": {"name": "Natural Gift", "effect": "O usuário canaliza o poder para atacar usando a Fruta que está segurando. A Fruta determina o tipo e o poder do movimento."}, "feint": {"name": "Feint", "effect": "Um ataque que acerta um alvo usando Proteção ou Detectar. <PERSON><PERSON> ta<PERSON> extingue os efeitos desses movimentos."}, "pluck": {"name": "<PERSON><PERSON>", "effect": "O usuário bica o alvo. Caso o alvo esteja segurando uma Fruta, o usuário a come e ganha seu efeito."}, "tailwind": {"name": "Tailwind", "effect": "O usuário forma um turbulento redemoinho que aumenta a Velocidade do usuário e de seus seus aliados por quatro turnos."}, "acupressure": {"name": "Acupressure", "effect": "O usuário aplica pressão em pontos de estresse, bruscamente fortalecendo um de seus atributos ou de seus aliados."}, "metalBurst": {"name": "Metal Burst", "effect": "O usuário revida com muito mais força contra o alvo que lhe infligiu dano por ultimo."}, "uTurn": {"name": "U-turn", "effect": "Depois de fazer o seu ataque, o usuário corre de volta para trocar de lugar com um Pokémon da própria equipe."}, "closeCombat": {"name": "Close Combat", "effect": "O usuário luta com o alvo de perto sem se defender. <PERSON><PERSON> diminui a Defesa e Defesa Especial do usuário."}, "payback": {"name": "Payback", "effect": "O usuário acumula poder, então ataca. Se o usuário se mover depois do alvo, o poder deste ataque será dobrado."}, "assurance": {"name": "Assurance", "effect": "Caso o alvo já tenha recebido dano no mesmo turno, o poder desse ataque é dobrado."}, "embargo": {"name": "Embargo", "effect": "Este movimento previne que o alvo use o seu item por cinco turnos. Seu Treinador também estará restrito de usar itens nele."}, "fling": {"name": "Fling", "effect": "O usuário arremessa seu item no alvo para atacar. O poder e o efeito deste movimento dependem do item utilizado."}, "psychoShift": {"name": "Psycho Shift", "effect": "Usando seu poder psíquico da sugestão, o usuário transfere suas condições de estado para o alvo."}, "trumpCard": {"name": "Trump Card", "effect": "Quanto menos PP este movimento tiver, maior será sua força."}, "healBlock": {"name": "Heal Block", "effect": "Por cinco turnos, o usuário previne que a equipe adversária use quaisquer movimentos, Habilidades, ou itens segurados para recuperar PS."}, "wringOut": {"name": "Wring Out", "effect": "O usuário torce o alvo com força bruta. Quando mais PS o alvo possuir, maior será o poder do movimento."}, "powerTrick": {"name": "Power Trick", "effect": "O usuário usufrui de seu poder psíquico para trocar os atributos de seu Ataque com sua Defesa."}, "gastroAcid": {"name": "Gastro Acid", "effect": "O usuário arremessa os ácidos de seu estômago no alvo. O fluido elimina o efeito da habilidade do alvo."}, "luckyChant": {"name": "<PERSON>", "effect": "O usuário recita um encantamento em direção ao céu, prevenindo que os Pokémon oponentes acertem golpes críticos."}, "meFirst": {"name": "Me First", "effect": "O usuário corta a ação do alvo para roubar seu movimento e usá-lo com maior poder. Esse movimento falha caso não seja usado primeiro."}, "copycat": {"name": "Copycat", "effect": "O usuário imita o movimento imediatamente usado antes dele. O movimento falha caso nenhum outro movimento tenha sido usado."}, "powerSwap": {"name": "Power Swap", "effect": "O usuário usufrui de seu poder psíquico para trocar mudanças de atributos feitas ao seu Ataque e Ataque Especial com os do alvo."}, "guardSwap": {"name": "Guard Swap", "effect": "O usuário usufrui de seu poder psíquico para trocar mudanças de atributos feitas à sua Defesa e Defesa Especial com as do alvo."}, "punishment": {"name": "Punishment", "effect": "Quanto mais os atributos do alvo estiverem fortalecidos, maior será o poder desse movimento."}, "lastResort": {"name": "Last Resort", "effect": "Este movimento somente pode ser usado depois de o usuário ter usado todos os outros movimentos que ele conhece em batalha."}, "worrySeed": {"name": "<PERSON><PERSON><PERSON>d", "effect": "Uma semente que causa preocupação é plantada no alvo. <PERSON><PERSON> previne o sono, fazendo a Habilidade do alvo se tornar Insônia."}, "suckerPunch": {"name": "Sucker Punch", "effect": "Esse movimento permite que o usuário ataque primeiro. Esse ataque falha caso o alvo não esteja preparando um ataque."}, "toxicSpikes": {"name": "Toxic Spikes", "effect": "O usuário planta uma armadilha de espinhos venenosos nos pés da equipe adversária. Os espinhos envenenam os Pokémon que entram em batalha."}, "heartSwap": {"name": "Heart Swap", "effect": "O usuário usufrui de seu poder psíquico para trocar mudanças de atributos com o oponente."}, "aquaRing": {"name": "Aqua Ring", "effect": "O usuário envolve-se em um véu feito de água. Ele recupera um pouco de PS a cada turno."}, "magnetRise": {"name": "Magnet <PERSON>", "effect": "O usuário levita usando magnetismo gerado por eletricidade por cinco turnos."}, "flareBlitz": {"name": "<PERSON><PERSON><PERSON>", "effect": "O usuário cobre o próprio corpo com chamas e avança no alvo. <PERSON><PERSON> também fere muito o usuário e pode deixar o alvo com uma queimadura."}, "forcePalm": {"name": "Force Palm", "effect": "O alvo é atacado com uma onda de choque. <PERSON><PERSON> também pode deixar o alvo com paralisia."}, "auraSphere": {"name": "Aura Sphere", "effect": "O usuário libera uma explosão de poder da aura de seu corpo no alvo. Esse ataque nunca erra."}, "rockPolish": {"name": "Rock Polish", "effect": "O usuário pule seu corpo para reduzir entraves. <PERSON><PERSON> pode aumentar bruscamente a Velocidade."}, "poisonJab": {"name": "Poison Jab", "effect": "O alvo é perfurado com um tentáculo ou braço banhado com veneno. <PERSON><PERSON> também pode envenenar o alvo."}, "darkPulse": {"name": "Dark Pulse", "effect": "O usuário descarrega uma horrível aura imbuída com pensamentos obscuros. <PERSON><PERSON> também pode fazer o alvo hesitar."}, "nightSlash": {"name": "Night Slash", "effect": "O usuário retalha o alvo no instante que surge uma oportunidade. Golpes críticos ocorrem mais facilmente."}, "aquaTail": {"name": "Aqua Tail", "effect": "O usuário ataca balançando sua cauda como se fosse uma violenta e furiosa tempestade."}, "seedBomb": {"name": "Seed Bomb", "effect": "O usuário atira uma barragem de sementes de casca dura acertando o alvo por cima."}, "airSlash": {"name": "Air Slash", "effect": "O usuário ataca com uma lâmina de ar que corta até mesmo o céu. <PERSON>so pode fazer o alvo hesitar."}, "xScissor": {"name": "X-Scissor", "effect": "O usuário cutila o alvo cruzando suas foices ou garras como se elas fossem um par de tesouras."}, "bugBuzz": {"name": "Bug <PERSON>", "effect": "O usuário gera uma dolorosa onda de som. <PERSON><PERSON> tamb<PERSON>m pode diminuir o atributo de Defesa Especial do alvo."}, "dragonPulse": {"name": "Dragon Pulse", "effect": "O alvo é atacado com uma onda de choque gerada pela boca aberta do usuário."}, "dragonRush": {"name": "Dragon Rush", "effect": "O usuário ataca o alvo enquanto demonstra uma ameaça esmagadora. <PERSON><PERSON> também pode fazer o alvo hesitar."}, "powerGem": {"name": "Power Gem", "effect": "O usuário ataca com um raio de luz que brilha como se fosse feito de pedras preciosas."}, "drainPunch": {"name": "<PERSON><PERSON>", "effect": "Um soco que drena energia. Os PS do usuário são curados pela metade do dano infligido ao alvo."}, "vacuumWave": {"name": "Vacuum Wave", "effect": "O usuário rodopia seus punhos para lançar uma onda de vácuo puro no alvo. Esse movimento tem prioridade."}, "focusBlast": {"name": "Focus Blast", "effect": "O usuário eleva seu foco mental e libera o seu poder. <PERSON><PERSON> tamb<PERSON>m pode diminuir a Defesa Especial do alvo."}, "energyBall": {"name": "Energy Ball", "effect": "O usuário extrai o poder da natureza e dispara no alvo. <PERSON><PERSON> tamb<PERSON>m pode diminuir a Defesa Especial do alvo."}, "braveBird": {"name": "<PERSON>", "effect": "O usuário dobra suas asas e avança de uma baixa altitude. <PERSON><PERSON> também fere muito o usuário."}, "earthPower": {"name": "Earth Power", "effect": "O usuário faz o solo debaixo do alvo emergir com poder. <PERSON><PERSON> também pode diminuir a Defesa Especial do alvo."}, "switcheroo": {"name": "Switcheroo", "effect": "O usuário troca de itens com o alvo mais rápido do que os olhos podem acompanhar."}, "gigaImpact": {"name": "Giga Impact", "effect": "O usuário investe no alvo usando absolutamente todo o seu poder. O usuário não poderá se mover no próximo turno."}, "nastyPlot": {"name": "Nasty Plot", "effect": "O usuário estimula seu cérebro com pensamentos malvados. Isso bruscamente aumenta o Ataque Especial do usuário."}, "bulletPunch": {"name": "Bullet Punch", "effect": "O usuário atinge o alvo com socos fortes tão rápidos como tiros. Esse movimento tem prioridade."}, "avalanche": {"name": "Avalanche", "effect": "Um ataque que inflige o dobro do dano caso o usuário já tenha sido ferido pelo alvo no mesmo turno."}, "iceShard": {"name": "Ice Shard", "effect": "O usuário congela rapidamente cristais de gelo e os arremessa no alvo. Esse movimento tem prioridade."}, "shadowClaw": {"name": "Shadow Claw", "effect": "O usuário corta com uma garra afiada feita de sombras. Golpes críticos ocorrem mais facilmente."}, "thunderFang": {"name": "Thunder Fang", "effect": "O usuário morde com presas eletrificadas. <PERSON><PERSON> tamb<PERSON>m pode fazer o alvo hesitar ou deixá-lo paralisado."}, "iceFang": {"name": "Ice Fang", "effect": "O usuário morde com presas infundidas com gelo. <PERSON><PERSON> tamb<PERSON>m pode fazer o alvo hesitar ou deixá-lo congelado."}, "fireFang": {"name": "Fire Fang", "effect": "O usuário morde com presas cobertas de fogo. <PERSON><PERSON> tamb<PERSON> pode fazer o alvo hesitar ou deixá-lo queimado."}, "shadowSneak": {"name": "Shadow Sneak", "effect": "O usuário estende a própria sombra e ataca o alvo por trás. Esse movimento tem prioridade."}, "mudBomb": {"name": "Mud Bomb", "effect": "O usuário lança uma bola concentrada de lama para atacar. <PERSON><PERSON> também pode diminuir a Precisão do alvo."}, "psychoCut": {"name": "Psycho Cut", "effect": "O usuário corta o alvo com lâminas materializadas com poder psíquico. Golpes críticos ocorrem mais facilmente."}, "zenHeadbutt": {"name": "<PERSON>butt", "effect": "O usuário foca sua força de vontade em sua cabeça e ataca o alvo. <PERSON><PERSON> também pode fazer o alvo hesitar."}, "mirrorShot": {"name": "Mirror Shot", "effect": "O usuário libera um clarão de energia vindo de seu corpo polido no alvo. <PERSON><PERSON> pode diminuir a Precisão do alvo."}, "flashCannon": {"name": "<PERSON>", "effect": "O usuário reúne toda a sua energia de luz e lança de uma só vez. <PERSON><PERSON> também pode diminuir a Defesa Especial do alvo."}, "rockClimb": {"name": "Rock Climb", "effect": "O usuário ataca o alvo o esmagando com uma incrível força bruta. Isso pode confundir o alvo."}, "defog": {"name": "Defog", "effect": "Um vento forte que dispersa as barreiras do alvo como Refletir ou Tela de Luz. Isso também diminui a Evasiva do oponente."}, "trickRoom": {"name": "Trick Room", "effect": "O usuário cria uma área bizarra onde Pokémon mais lentos se movem primeiro por cinco turnos."}, "dracoMeteor": {"name": "Draco Meteor", "effect": "Cometas são invocados do céu e caem sobre o alvo. O efeito colateral do ataque duramente prejudica o atributo de Ataque Especial do usuário."}, "discharge": {"name": "Discharge", "effect": "O usuário atinge tudo ao seu redor liberando uma explosão de eletricidade. <PERSON><PERSON> também pode causar paralisia."}, "lavaPlume": {"name": "<PERSON><PERSON>", "effect": "O usuário queima tudo ao seu redor com um inferno de chamas escarlate. <PERSON><PERSON> também pode causar uma queimadura em alvos atingidos."}, "leafStorm": {"name": "Leaf Storm", "effect": "O usuário cria uma tempestade de folhas ao redor do alvo. <PERSON><PERSON> diminui duramente o Ataque Especial do usuário."}, "powerWhip": {"name": "Power Whip", "effect": "O usuário rodopia suas vinhas ou tentáculos com vigor para chicotear o alvo cruelmente."}, "rockWrecker": {"name": "<PERSON> Wrecker", "effect": "O usuário lança uma grande rocha no alvo. O usuário não pode se mover no próximo turno."}, "crossPoison": {"name": "Cross Poison", "effect": "Um ataque cortante com uma lâmina envenenada que pode envenenar o alvo. Golpes críticos ocorrem mais facilmente."}, "gunkShot": {"name": "Gunk Shot", "effect": "O usuário atira lixo repugnante no alvo. <PERSON><PERSON> tamb<PERSON>m pode envenenar o alvo."}, "ironHead": {"name": "Iron Head", "effect": "O usuário acerta o alvo com sua cabeça dura como aço. <PERSON><PERSON> também pode fazer o alvo hesitar."}, "magnetBomb": {"name": "Magnet <PERSON>", "effect": "O usuário lança bombas de aço que grudam no alvo. Esse ataque nunca erra."}, "stoneEdge": {"name": "<PERSON>", "effect": "O usuário perfura o alvo por baixo com pedras afiadas. Golpes críticos ocorrem mais facilmente."}, "captivate": {"name": "Captivate", "effect": "Se algum dos Pokémon oponentes forem do gênero oposto do usuário, ele se encanta, o que diminui duramente o seu Ataque Especial."}, "stealthRock": {"name": "Stealth Rock", "effect": "O usuário planta armadilhas de pedras levitantes ao redor da equipe oponente. A armadilha fere os Pokémon oponentes que entrarem no campo de batalha."}, "grassKnot": {"name": "<PERSON> Knot", "effect": "O usuário planta uma armadilha com grama e o alvo tropeça nela. Quanto mais pesado o alvo for, maior é o poder do movimento."}, "chatter": {"name": "Chatter", "effect": "O usuário ataca o alvo com ondas de som vindas de sua tagarelagem ensurdecedora. Isso confunde o alvo."}, "judgment": {"name": "Judgment", "effect": "O usuário libera incontáveis tiros de luz no alvo. A tipagem desse movimento varia dependendo do tipo de Placa que o usuário está segurando."}, "bugBite": {"name": "<PERSON><PERSON> Bite", "effect": "O usuário morde o alvo. Caso o alvo esteja segurando uma Fruta, o usuário a come e ganha seu efeito."}, "chargeBeam": {"name": "Charge Beam", "effect": "O usuário ataca com uma carga elétrica. O usuário pode usar a eletricidade que sobrar para fortalecer seu Ataque Especial."}, "woodHammer": {"name": "<PERSON>", "effect": "O usuário usa seu corpo rígido para golpear o alvo. <PERSON><PERSON> também fere muito o usuário."}, "aquaJet": {"name": "Aqua Jet", "effect": "O usuário ataca o alvo em uma velocidade que o torna quase invisível. Esse movimento tem prioridade."}, "attackOrder": {"name": "Attack Order", "effect": "O usuário ordena que seus subordinados ataquem o alvo. Golpes críticos ocorrem mais facilmente."}, "defendOrder": {"name": "Defend Order", "effect": "O usuário ordena que seus subordinados protejam seu corpo, aumentando seus atributos de Defesa e Defesa Especial."}, "healOrder": {"name": "Heal Order", "effect": "O usuário ordena que seus subordinados o curem. O usuário restaura até metade de seus PS máximos."}, "headSmash": {"name": "Head Smash", "effect": "O usuário usa toda a sua força para acertar uma perigosa cabeçada destrutiva no alvo. <PERSON><PERSON> também fere terrivelmente o usuário."}, "doubleHit": {"name": "Double Hit", "effect": "O usuário golpeia o alvo com uma longa cauda, vinhas, ou um tentáculo. O alvo é acertado duas vezes seguidas."}, "roarOfTime": {"name": "Roar of Time", "effect": "O usuário bombardeia o alvo com tamanho poder que distorce até mesmo o tempo; porém, não se moverá no próximo turno."}, "spacialRend": {"name": "Spacial Rend", "effect": "O usuário rasga a existência do alvo junto com o espaço ao seu redor. Golpes críticos ocorrem mais facilmente."}, "lunarDance": {"name": "Lunar Dance", "effect": "O usuário desmaia. Em troca, o Pokémon que tomará seu lugar terá suas condições negativas e PS restaurados."}, "crushGrip": {"name": "Crush Grip", "effect": "O alvo é esmagado com grande força. Quanto mais PS o alvo possuir, maior será o poder desse movimento."}, "magmaStorm": {"name": "Magma Storm", "effect": "O alvo é preso dentro de um turbilhão de fogo que arde de quatro a cinco turnos."}, "darkVoid": {"name": "Dark Void", "effect": "Pokémon oponentes são sugados para dentro de um mundo de total escuridão que os faz dormir."}, "seedFlare": {"name": "Seed Flare", "effect": "O usuário emite uma onda de choque de seu corpo para atacar o alvo. <PERSON><PERSON> também pode diminuir duramente a Defesa Especial do alvo."}, "ominousWind": {"name": "Ominous Wind", "effect": "O usuário ataca o alvo com uma rajada de vento repulsivo. Talvez aumente todos os atributos do usuário de uma vez."}, "shadowForce": {"name": "Shadow Force", "effect": "O usuário desaparece, então atinge o alvo no próximo turno. Esse movimento acerta o alvo mesmo que ele proteja a si mesmo."}, "honeClaws": {"name": "Hone Claws", "effect": "O usuário afia suas garras para fortalecer seu Ataque e sua Precisão."}, "wideGuard": {"name": "Wide Guard", "effect": "O usuário e seus aliados são protegidos de ataques de longo alcance por um turno."}, "guardSplit": {"name": "Guard Split", "effect": "O usuário usufrui de seu poder psíquico para equalizar seus atributos de Defesa e Defesa Especial com o alvo."}, "powerSplit": {"name": "Power Split", "effect": "O usuário usufrui de seu poder psíquico para igualar seus atributos de Ataque e Ataque Especial com o alvo."}, "wonderRoom": {"name": "Wonder Room", "effect": "O usuário cria uma área bizarra onde os atributos de Defesa e Defesa Especial dos Pokémon são trocados por cinco turnos."}, "psyshock": {"name": "Psyshock", "effect": "O usuário materializa uma estranha onda psíquica para atacar o alvo. Esse ataque inflige dano físico."}, "venoshock": {"name": "Venoshock", "effect": "O usuário encharca o alvo com um líquido venenoso especial. O poder desse movimento dobra se o alvo estiver envenenado."}, "autotomize": {"name": "Autotomize", "effect": "O usuário perde parte de seu corpo para se tornar mais leve e bruscamente aumentar seu atributo de Velocidade."}, "ragePowder": {"name": "<PERSON>w<PERSON>", "effect": "O usuário espalha uma nuvem de pó irritante para chamar a atenção para si mesmo. Oponentes miram apenas no usuário."}, "telekinesis": {"name": "Telekinesis", "effect": "O usuário faz o alvo flutuar usando o seu poder psíquico. O alvo fica propício a ser atingido por três turnos."}, "magicRoom": {"name": "Magic Room", "effect": "O usuário cria uma área bizarra onde os itens dos Pokémon perdem seus efeitos por cinco turnos."}, "smackDown": {"name": "Smack Down", "effect": "O usuário atira uma pedra ou algum projétil similar para atacar o oponente. Um Pokémon voador irá cair no chão quando for acertado."}, "stormThrow": {"name": "Storm Throw", "effect": "O usuário atinge o alvo com um golpe poderoso. Esse ataque sempre resulta em um golpe critico."}, "flameBurst": {"name": "Flame Burst", "effect": "O usuário ataca o alvo com uma chama explosiva. A explosão da chama também fere os Pokémon próximos ao alvo."}, "sludgeWave": {"name": "Sludge Wave", "effect": "O usuário atinge tudo à volta inundando a área com uma grande onda de sedimentos. <PERSON><PERSON> também pode envenenar os atingidos."}, "quiverDance": {"name": "Quiver Dance", "effect": "O usuário delicadamente executa uma linda dança mística. Is<PERSON> fortalece os atributos de Ataque Especial, Defesa Especial e Velocidade do usuário."}, "heavySlam": {"name": "Heavy Slam", "effect": "O usuário golpeia o alvo com seu corpo pesado. Quanto mais pesado o usuário for comparado ao alvo, maior será o poder do movimento."}, "synchronoise": {"name": "Synchronoise", "effect": "Usando uma estranha onda de choque, o usuário inflige dano em qualquer Pokémon do mesmo tipo na área ao seu redor."}, "electroBall": {"name": "Electro Ball", "effect": "O usuário arremessa uma esfera elétrica no alvo. Quanto mais rápido for o usuário comparado ao alvo, maior será o poder do movimento."}, "soak": {"name": "Soak", "effect": "O usuário atira uma corrente de água no alvo e muda a tipagem do alvo para Água."}, "flameCharge": {"name": "Flame Charge", "effect": "Ocultando-se nas chamas, o usuário ataca. Então, concentrando mais poder, o usuário aumenta sua Velocidade."}, "coil": {"name": "Coil", "effect": "O usuário enrola seu corpo e se concentra. Isso aumenta seus atributos de Ataque, Defesa e Precisão."}, "lowSweep": {"name": "Low Sweep", "effect": "O usuário faz um ataque repentino nas pernas do alvo, diminuindo a Velocidade dele."}, "acidSpray": {"name": "Acid <PERSON>y", "effect": "O usuário cospe um fluido corrosivo no alvo. <PERSON><PERSON> duramente diminui a Defesa Especial do alvo."}, "foulPlay": {"name": "Foul Play", "effect": "O usuário vira o poder do alvo contra ele. Quanto maior for o atributo de Ataque do alvo, maior será o poder do movimento."}, "simpleBeam": {"name": "Simple Beam", "effect": "Essa misteriosa onda psíquica produzida pelo usuário muda a Habilidade do alvo para “Simples”."}, "entrainment": {"name": "Entrainment", "effect": "O usuário dança em um ritmo estranho que contagia o alvo que o imita, fazendo a Habilidade do alvo tornar-se a mesma que a do usuário."}, "afterYou": {"name": "After You", "effect": "O usuário auxilia o alvo e o faz usar seu movimento exatamente após o usuário."}, "round": {"name": "Round", "effect": "O usuário ataca o alvo com uma música. Outros podem entrar na Ronda e fazer o ataque dar um dano ainda maior."}, "echoedVoice": {"name": "Echoed Voice", "effect": "O usuário ataca o alvo com uma voz ecoante. Se esse movimento for usado um turno após o outro, ele infligirá dano maior."}, "chipAway": {"name": "Chip Away", "effect": "Procurando por uma brecha, o usuário ataca consistentemente. As mudanças de atributos do alvo não afetam o dano desse movimento."}, "clearSmog": {"name": "Clear Smog", "effect": "O usuário ataca arremessando um amontoado de lama especial. <PERSON><PERSON> as mudanças de atributos voltam ao normal."}, "storedPower": {"name": "Stored Power", "effect": "O usuário ataca o alvo com seu poder armazenado. Quanto mais os atributos do usuário estiverem fortalecidos, maior será o poder do movimento."}, "quickGuard": {"name": "Quick Guard", "effect": "O usuário protege a si mesmo e seus aliados de golpes de prioridade."}, "allySwitch": {"name": "<PERSON>", "effect": "O usuário teletransporta usando um estranho poder e troca de lugar com um de seus aliados."}, "scald": {"name": "<PERSON><PERSON>", "effect": "O usuário atira água fervente no seu alvo. <PERSON><PERSON> também pode deixar o alvo com queimadura."}, "shellSmash": {"name": "Shell Smash", "effect": "O usuário quebra a própria concha, diminuindo sua Defesa e Defesa Especial, mas bruscamente aumentando Ataque, Ataque Especial e Velocidade."}, "healPulse": {"name": "Heal Pulse", "effect": "O usuário emite um pulso curativo que restaura os PS do alvo pela metade de seus PS máximos."}, "hex": {"name": "Hex", "effect": "Esse ataque cruel inflige dano massivo a um alvo afetado por condições negativas."}, "skyDrop": {"name": "Sky Drop", "effect": "O usuário leva o alvo para o céu, então o solta durante o próximo turno. O alvo não pode atacar enquanto estiver no céu."}, "shiftGear": {"name": "Shift Gear", "effect": "O usuário roda suas engrenagens, aumentando seu Ataque e bruscamente aumentando sua Velocidade."}, "circleThrow": {"name": "Circle Throw", "effect": "O alvo é arremessado, e um Pokémon diferente é trazido para a batalha. <PERSON> <PERSON>za, isso termina uma batalha contra um único Pokémon."}, "incinerate": {"name": "Incinerate", "effect": "O usuário ataca o Pokémon oponente com fogo. Se um Pokémon estiver segurando um certo item, como uma Fruta, o item será queimado e inutilizado."}, "quash": {"name": "<PERSON><PERSON>sh", "effect": "O usuário reprime o alvo e o faz se mover por último."}, "acrobatics": {"name": "Acrobatics", "effect": "O usuário atinge o alvo rapidamente. Se o usuário não estiver segurando um item, esse ataque causa um dano massivo."}, "reflectType": {"name": "Reflect Type", "effect": "O usuário reflete o tipo do alvo, fazendo-o ter o mesmo tipo do alvo."}, "retaliate": {"name": "Retaliate", "effect": "O usuário se vinga por um aliado desmaiado. Se um aliado desmaiou no turno anterior, esse movimento ficará mais poderoso."}, "finalGambit": {"name": "Final Gambit", "effect": "O usuário arrisca tudo para atacar seu alvo. O usuário desmaia porém inflige dano igual aos seus PS perdidos."}, "bestow": {"name": "Bestow", "effect": "O usuário passa seu item ao alvo se o alvo não estiver segurando um item."}, "inferno": {"name": "Inferno", "effect": "O usuário ataca engolindo o alvo em intensas chamas. intense fire. Isso deixa o alvo com uma queimadura."}, "waterPledge": {"name": "Water Pledge", "effect": "Um pilar de água atinge o alvo. Quando combinado com seu equivalente do tipo fogo, seu dano aumenta e um arco-íris é formado."}, "firePledge": {"name": "Fire Pledge", "effect": "Um pilar de fogo atinge o alvo. Quando combinado com seu equivalente do tipo Planta, seu dano aumenta e um vasto mar de fogo aparece."}, "grassPledge": {"name": "<PERSON>", "effect": "Um pilar de grama acerta o alvo. Quando combinado com seu equivalente do tipo Água, seu dano aumenta e um vasto pântano surge."}, "voltSwitch": {"name": "Volt Switch", "effect": "Depois de fazer o seu ataque, o usuário corre de volta para trocar de lugar com um Pokémon da própria equipe."}, "struggleBug": {"name": "<PERSON>rug<PERSON> Bug", "effect": "Enquanto resiste, o usuário ataca o Pokémon oponente. <PERSON><PERSON> diminui o Ataque Especial daqueles atingidos."}, "bulldoze": {"name": "Bulldoze", "effect": "O usuário atinge a todos ao seu redor pisoteando o chão. Isso diminui a Velocidade daqueles atingidos."}, "frostBreath": {"name": "<PERSON>", "effect": "O usuário sopra sua respiração gelada no alvo. Esse ataque sempre resulta em um acerto crítico."}, "dragonTail": {"name": "Dragon Tail", "effect": "O alvo é arremessado e um Pokémon diferente é trazido para o combate. Em batalhas selvagens, isso encerra a batalha contra um único Pokémon."}, "workUp": {"name": "Work Up", "effect": "O usuário se agita e seus atributos de Ataque e Ataque Especial são fortalecidos."}, "electroweb": {"name": "Electroweb", "effect": "O usuário ataca e captura os Pokémon adversários usando uma rede elétrica. Isso diminui a Velocidade deles."}, "wildCharge": {"name": "Wild Charge", "effect": "O usuário se cobre de eletricidade e colide com o seu alvo. <PERSON><PERSON> também fere um pouco o usuário."}, "drillRun": {"name": "Drill Run", "effect": "O usuário colide com seu alvo enquanto rotaciona seu corpo como uma broca. Golpes críticos acertam mais facilmente."}, "dualChop": {"name": "Dual Chop", "effect": "O usuário ataca o seu alvo o acertando com golpes brutais. O alvo é atingido duas vezes seguidas."}, "heartStamp": {"name": "Heart Stamp", "effect": "O usuário libera sua fúria em um golpe violento após enganar o alvo com sua atuação fofa. <PERSON><PERSON> também pode fazer o alvo hesitar."}, "hornLeech": {"name": "<PERSON>", "effect": "O usuário drena a energia do alvo com seus chifres. Os PS do usuário são restaurados pela metade do dano recebido pelo alvo."}, "sacredSword": {"name": "Sacred Sword", "effect": "O usuário ataca cortando com um longo chifre. As mudanças de atributos do alvo não afetam o dano desse ataque."}, "razorShell": {"name": "Razor Shell", "effect": "O usuário corta seu alvo com conchas afiadas. <PERSON><PERSON> pode também diminuir o atributo de Defesa do alvo."}, "heatCrash": {"name": "Heat Crash", "effect": "O usuário golpeia seu alvo com seu corpo envolto em chamas. Quanto mais pesado o usuário for comparado ao alvo, maior será o poder do movimento."}, "leafTornado": {"name": "Leaf Tornado", "effect": "O usuário ataca seu alvo cercando-o com folhas afiadas. Isso também pode diminuir a precisão do alvo."}, "steamroller": {"name": "Steamroller", "effect": "O usuário esmaga seu alvo rolando sobre ele com seu corpo enrolado como uma bola. <PERSON><PERSON> também pode fazer o alvo hesitar."}, "cottonGuard": {"name": "Cotton Guard", "effect": "O usuário protege a si mesmo envolvendo seu corpo em algodão macio, o que drasticamente aumenta o atributo de Defesa do usuário."}, "nightDaze": {"name": "Night Daze", "effect": "O usuário libera uma onda de choque escura como a noite no alvo. <PERSON><PERSON> também pode reduzir a Precisão do alvo."}, "psystrike": {"name": "Psystrike", "effect": "O usuário materializa uma estranha onda psíquica para atacar o alvo. Esse ataque inflige dano físico."}, "tailSlap": {"name": "<PERSON><PERSON>", "effect": "O usuário ataca golpeando o alvo com sua cauda resistente. <PERSON>so acerta o alvo duas a cinco vezes seguidas."}, "hurricane": {"name": "Hurricane", "effect": "O usuário ataca prendendo seu oponente num violento turbilhão que voa alto no céu. <PERSON><PERSON> também pode confundir o alvo."}, "headCharge": {"name": "Head Charge", "effect": "O usuário ataca colidindo sua cabeça no alvo, usando sua pelagem protetora. <PERSON><PERSON> também fere um pouco o usuário."}, "gearGrind": {"name": "Gear Grind", "effect": "O usuário ataca arremessando engrenagens de aço no seu alvo duas vezes em sequência."}, "searingShot": {"name": "Searing Shot", "effect": "O usuário queima tudo ao seu redor com um inferno de chamas escarlate. <PERSON><PERSON> também pode causar uma queimadura em alvos atingidos."}, "technoBlast": {"name": "Techno Blast", "effect": "O usuário atira um raio de luz em seu alvo. O tipo do movimento muda dependendo do Disco que o usuário estiver segurando."}, "relicSong": {"name": "Relic Song", "effect": "O usuário canta uma antiga canção e ataca encantando o coração dos Pokémon adversários. <PERSON><PERSON> também pode induzir sono."}, "secretSword": {"name": "Secret Sword", "effect": "O usuário ataca cortando com seu longo chifre. O estranho poder contido no chifre inflige dano físico no alvo."}, "glaciate": {"name": "Glaciate", "effect": "O usuário ataca soprando ar congelante nos Pokémon oponentes. Isso diminui a Velocidade deles."}, "boltStrike": {"name": "Bolt Strike", "effect": "O usuário cobre a si mesmo com uma grande quantidade de eletricidade e avança no alvo. <PERSON><PERSON> também pode paralisar o alvo."}, "blueFlare": {"name": "Blue Flare", "effect": "O usuário ataca engolindo o alvo numa intensa, porém linda, chama azul. <PERSON><PERSON> também pode deixar o alvo com uma queimadura."}, "fieryDance": {"name": "Fiery Dance", "effect": "<PERSON><PERSON> por chama<PERSON>, o usuário dança e bate suas asas. <PERSON><PERSON> também pode aumentar o Ataque Especial do usuário."}, "freezeShock": {"name": "Freeze Shock", "effect": "No segundo turno, o usuário acerta o alvo com gelo eletricamente carregado. <PERSON><PERSON> também pode deixar o alvo paralisado."}, "iceBurn": {"name": "Ice Burn", "effect": "No segundo turno, um impiedoso vento gélido cerca o alvo. <PERSON><PERSON> pode deixar o alvo com uma queimadura."}, "snarl": {"name": "Snarl", "effect": "O usuário grita como se ele estivesse reclamando de algo, diminuindo o Ataque Especial do Pokémon oponente."}, "icicleCrash": {"name": "Icicle Crash", "effect": "O usuário ataca arremessando estacas de gelo no alvo violentamente. <PERSON><PERSON> também pode fazer o alvo hesitar."}, "vCreate": {"name": "V-create", "effect": "Com uma ardente chama em sua testa, o usuário joga seu corpo em direção ao alvo. <PERSON><PERSON> diminui a <PERSON>, Defesa Especial, e Velocidade do usuário."}, "fusionFlare": {"name": "Fusion Flare", "effect": "O usuário invoca uma chama gigante. Esse movimento é mais poderoso quando influenciado por um enorme raio."}, "fusionBolt": {"name": "Fusion Bolt", "effect": "O usuário conduz um raio gigantesco. Esse movimento é mais poderoso quando influenciado por uma enorme chama."}, "flyingPress": {"name": "Flying Press", "effect": "O usuário mergulha do céu em direção ao alvo. Esse movimento é simultaneamente do tipo Lutador e Voador."}, "matBlock": {"name": "Mat Block", "effect": "Usando uma esteira elevada como escudo, o usuário protege a si mesmo e a seus aliados de golpes que causam dano. Isso não previne condições negativas."}, "belch": {"name": "Belch", "effect": "O usuário expurga um arroto danificante no alvo. O usuário deve comer uma Fruta para usar esse movimento."}, "rototiller": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "O usuário ara o solo, facilitando o crescimento de plantas. Isso aumenta os atributos de Ataque e Ataque Especial dos Pokémon do tipo Planta."}, "stickyWeb": {"name": "Sticky Web", "effect": "O usuário tece uma teia viscosa ao redor da equipe adversária, o que diminui a Velocidade dos adversários após entrarem no campo de batalha."}, "fellStinger": {"name": "<PERSON> Stinger", "effect": "Quando o usuário nocauteia um alvo com este movimento, o atributo de Ataque do usuário aumenta bruscamente."}, "phantomForce": {"name": "Phantom Force", "effect": "O usuário desaparece para algum lugar e então ataca o alvo no próximo turno. Esse movimento acerta mesmo se o alvo estiver se protegendo."}, "trickOrTreat": {"name": "Trick-or-Treat", "effect": "O usuário enche o alvo com o espírito do Halloween para celebrarem juntos. Isso adiciona o tipo Fantasma à tipagem do alvo."}, "nobleRoar": {"name": "<PERSON>", "effect": "Soltando um nobre rugido, o usuário intimida o alvo e diminui seus atributos de Ataque e Ataque Especial."}, "ionDeluge": {"name": "<PERSON>", "effect": "O usuário dispersa partículas eletricamente carregadas, o que muda movimentos do tipo Normal para o tipo Elétrico."}, "parabolicCharge": {"name": "Parabolic Charge", "effect": "O usuário ataca tudo ao seu redor. Os PS do usuário são restaurados pela metade do dano recebido por aqueles que foram atingidos."}, "forestsCurse": {"name": "Forest’s Curse", "effect": "O usuário conjura uma maldição da floresta no alvo. Isso adiciona o tipo Planta à tipagem do alvo."}, "petalBlizzard": {"name": "Petal Blizzard", "effect": "O usuário rotaciona uma violenta nevasca composta por pétalas e ataca tudo ao seu redor."}, "freezeDry": {"name": "Freeze-Dry", "effect": "O usuário rapidamente diminui a temperatura do alvo. <PERSON><PERSON> pode deixar o alvo congelado. Esse movimento é supereficaz contra tipos Água."}, "disarmingVoice": {"name": "Disarming Voice", "effect": "Liberando um grito encantador, o usuário inflige dano emocional nos Pokémon oponentes. Esse ataque nunca erra."}, "partingShot": {"name": "Parting Shot", "effect": "O usuário diminui os atributos de Ataque e Ataque Esp. do alvo com uma ameaça antes de ser trocado por outro Pokémon na equipe."}, "topsyTurvy": {"name": "Topsy-<PERSON><PERSON><PERSON>", "effect": "<PERSON><PERSON> as mudanças de atributos afetando o alvo viram de cabeça para baixo e se tornam o oposto do que eram."}, "drainingKiss": {"name": "Draining Kiss", "effect": "O usuário rouba a energia do alvo com um beijo. Os PS do usuário são restaurados além da metade do dano recebido pelo alvo."}, "craftyShield": {"name": "Crafty Shield", "effect": "O usuário protege a si mesmo e seus aliados de condições negativas com um misterioso poder. Isso não previne golpes que inflijam dano."}, "flowerShield": {"name": "Flower Shield", "effect": "Usando um misterioso poder, o usuário aumenta o atributo de Defesa de todos os Pokémon tipo Planta em batalha."}, "grassyTerrain": {"name": "<PERSON>y <PERSON>", "effect": "O usuário transforma o campo de batalha em grama por cinco turnos. <PERSON><PERSON> restaura os PS dos Pokémon no solo um pouco a cada turno e fortalece golpes do tipo Grama."}, "mistyTerrain": {"name": "<PERSON>", "effect": "Isto protege os Pokémon no solo de condições de estado e corta pela metade o dano dos movimentos do tipo Dragão por cinco turnos."}, "electrify": {"name": "Electrify", "effect": "Caso o alvo tenha sido energizado antes de usar um movimento durante aquele turno, o movimento do alvo se tornará do tipo Elétrico."}, "playRough": {"name": "Play Rough", "effect": "O usuário joga duro com o alvo e o ataca. <PERSON><PERSON> também pode diminuir o atributo de Ataque do alvo."}, "fairyWind": {"name": "Fairy Wind", "effect": "O usuário rotaciona um vento de fada e ataca o alvo com ele."}, "moonblast": {"name": "Moonblast", "effect": "Canalizando o poder da lua, o usuário ataca o alvo. <PERSON><PERSON> tamb<PERSON>m pode diminuir o atributo de Ataque Especial do alvo."}, "boomburst": {"name": "Boomburst", "effect": "O usuário ataca tudo ao seu redor com o poder destrutivo de um terrível som explosivo."}, "fairyLock": {"name": "Fairy Lock", "effect": "Bloqueando o campo de batalha, o usuário previne que todos os Pokémon fujam durante o próximo turno."}, "kingsShield": {"name": "King’s Shield", "effect": "O usuário assume uma posição defensiva enquanto protege a si mesmo de dano. Isto duramente diminui o Ataque de qualquer um que faça contato direto."}, "playNice": {"name": "Play Nice", "effect": "O usuário e o alvo se tornam amigos, fazendo com que o alvo perca sua vontade de lutar. <PERSON><PERSON> diminui o atributo de Ataque do alvo."}, "confide": {"name": "Confide", "effect": "O usuário conta um segredo para o alvo e o alvo perde sua habilidade de se concentrar. <PERSON><PERSON> diminui o Ataque Especial do alvo."}, "diamondStorm": {"name": "Diamond Storm", "effect": "O usuário provoca uma tempestade de diamantes para ferir os Pokémon oponentes. <PERSON><PERSON> também pode aumentar o atributo de Defesa do usuário."}, "steamEruption": {"name": "Steam Eruption", "effect": "O usuário imerge o alvo em vapor superaquecido. <PERSON><PERSON> também pode deixar o alvo com uma queimadura."}, "hyperspaceHole": {"name": "Hyperspace Hole", "effect": "Usando uma fenda espacial, o usuário aparece ao lado do alvo e ataca. Isso também acerta um alvo usando movimentos como Proteger ou Detectar."}, "waterShuriken": {"name": "Water Shuriken", "effect": "O usuário acerta o alvo jogando estrelas ninja de duas a cinco vezes seguidas. Esse movimento tem prioridade."}, "mysticalFire": {"name": "Mystical Fire", "effect": "O usuário ataca soprando um fogo ardente especial. <PERSON><PERSON> também diminui o Ataque Especial do alvo."}, "spikyShield": {"name": "Spiky Shield", "effect": "Além de proteger o alvo de ataques, este movimento também fere qualquer atacante que fizer contato direto."}, "aromaticMist": {"name": "Aromatic Mist", "effect": "Usando um misterioso aroma, o usuário aumenta o atributo de Defesa Especial de um Pokémon aliado."}, "eerieImpulse": {"name": "<PERSON><PERSON>", "effect": "O corpo do usuário gera um impulso misterioso. O alvo exposto ao impulso tem seu Ataque Especial duramente diminuído."}, "venomDrench": {"name": "<PERSON><PERSON><PERSON>", "effect": "Pokémon oponentes são encharcados por um estranho líquido venenoso. Is<PERSON> diminui o Ataque, Ataque Esp. e Velocidade de um alvo envenenado."}, "powder": {"name": "<PERSON><PERSON><PERSON>", "effect": "O usuário cobre o alvo em pólvora. Se o alvo usar um movimento do tipo Fogo, a pólvora entra em combustão e causa dano ao alvo."}, "geomancy": {"name": "Geomancy", "effect": "O usuário absorve energia e bruscamente aumenta seus atributos de Ataque Especial, Defesa Especial e Velocidade no próximo turno."}, "magneticFlux": {"name": "Magnetic Flux", "effect": "O usuário manipula campos magnéticos, o que aumenta os atributos de Defesa e Defesa Especial de Pokémon aliados com as Habilidades “<PERSON>s” ou “Menos”."}, "happyHour": {"name": "Happy Hour", "effect": "Usar Happy Hour dobra a quantidade de prêmio em dinheiro recebido após a batalha."}, "electricTerrain": {"name": "Electric Terrain", "effect": "O usuário eletrifica o campo de batalha por cinco turnos, fortalecendo movimentos do tipo Elétrico. Pokémon no solo não podem mais cair no sono."}, "dazzlingGleam": {"name": "Dazzling Gleam", "effect": "O usuário causa dano ao Pokémon oponente emitindo um clarão poderoso."}, "celebrate": {"name": "Celebrate", "effect": "O Pokémon te dá parabéns pelo seu dia especial!"}, "holdHands": {"name": "Hold Hands", "effect": "O usuário e um aliado dão as mãos. Isso os deixam muito contentes."}, "babyDollEyes": {"name": "Baby-<PERSON>", "effect": "O usuário encara o alvo com seus olhos adoráve<PERSON>, o que diminui seu atributo de Ataque. Esse movimento tem prioridade."}, "nuzzle": {"name": "Nuzzle", "effect": "O usuário ataca esfregando suas bochechas eletrizadas contra o alvo. Isso também deixa o alvo paralisado."}, "holdBack": {"name": "Hold Back", "effect": "O usuário pega leve quando ataca e o alvo é deixado com pelo menos 1 PS."}, "infestation": {"name": "Infestation", "effect": "O alvo é infestado e atacado de quatro a cinco turnos. O alvo não pode fugir durante esse período."}, "powerUpPunch": {"name": "Power-Up Punch", "effect": "Golpear oponentes repetidamente faz os punhos do usuário enrijecerem. Acertar um alvo aumenta o Ataque."}, "oblivionWing": {"name": "Oblivion Wing", "effect": "O usuário absorve os PS de seu alvo. Os PS do usuário são restaurados além da metade do dano recebido pelo usuário."}, "thousandArrows": {"name": "Thousand Arrows", "effect": "Este movimento também acerta Pokémon adversários que estão no ar. Esses Pokémon são derrubados e caem no chão."}, "thousandWaves": {"name": "Thousand Waves", "effect": "O usuário ataca com tremores que se dispersam pelo chão. Alvos atingidos não podem fugir da batalha."}, "landsWrath": {"name": "Land’s Wrath", "effect": "O usuário reúne a energia da terra e foca esse poder nos Pokémon oponentes para causar dano."}, "lightOfRuin": {"name": "Light of Ruin", "effect": "Usufruindo do poder da flor eterna, o usuário atira um poderoso raio de luz. <PERSON><PERSON> tamb<PERSON>m fere muito o usuário."}, "originPulse": {"name": "Origin Pulse", "effect": "O usuário ataca o Pokémon adversário com inúmeros raios de luz resplandescente que brilham em uma cor de profundo azul."}, "precipiceBlades": {"name": "Precipice Blades", "effect": "O usuário ataca o Pokémon adversário manifestando o poder terrestre em espadas de pedra assustadoras."}, "dragonAscent": {"name": "Dragon Ascent", "effect": "Depois de alcançar grandes alturas, o usuário ataca o alvo mergulhando do céu em alta velocidade, porém isso diminui sua própria Defesa e Defesa Especial."}, "hyperspaceFury": {"name": "Hyperspace Fury", "effect": "Usando seus diversos braços, o usuário libera golpes furiosos que ignoram efeitos de movimentos como Proteção e Detectar. Diminui a Defesa do usuário."}, "breakneckBlitzPhysical": {"name": "Breakneck Blitz", "effect": "Utilizando o Poder Z, o usuário intensifica seu ímpeto e atinge o alvo em alta velocidade. Seu poder varia dependendo do movimento original."}, "breakneckBlitzSpecial": {"name": "Breakneck Blitz", "effect": "Dummy <PERSON>"}, "allOutPummelingPhysical": {"name": "All-Out Pummeling", "effect": "Utilizando o Poder Z, o usuário cria e arremessa uma esfera de energia no alvo com força total. Seu poder varia dependendo do movimento original."}, "allOutPummelingSpecial": {"name": "All-Out Pummeling", "effect": "Dummy <PERSON>"}, "supersonicSkystrikePhysical": {"name": "Supersonic Skystrike", "effect": "Utilizando o Poder Z, o usuário ascende e mergulha em direção ao alvo em alta velocidade. Seu poder varia dependendo do movimento original."}, "supersonicSkystrikeSpecial": {"name": "Supersonic Skystrike", "effect": "Dummy <PERSON>"}, "acidDownpourPhysical": {"name": "Acid Downpour", "effect": "Utilizando o Poder Z, o usuário cria um pântano venenoso e afoga o alvo com toda sua força. Seu poder varia dependendo do movimento original."}, "acidDownpourSpecial": {"name": "Acid Downpour", "effect": "Dummy <PERSON>"}, "tectonicRagePhysical": {"name": "Tectonic Rage", "effect": "Utilizando o Poder Z, o usuário abre uma cratera e mergulha nela com o alvo com força total. Seu poder varia dependendo do movimento original."}, "tectonicRageSpecial": {"name": "Tectonic Rage", "effect": "Dummy <PERSON>"}, "continentalCrushPhysical": {"name": "Continental Crush", "effect": "Utilizando o Poder Z, o usuário cria um meteoro que cai do céu em direção ao alvo com força total. Seu poder varia dependendo do movimento original."}, "continentalCrushSpecial": {"name": "Continental Crush", "effect": "Dummy <PERSON>"}, "savageSpinOutPhysical": {"name": "Savage Spin-Out", "effect": "Utilizando o Poder Z, o usuário restringe o alvo em um casulo de seda e ataca ele com força total. Seu poder varia dependendo do movimento original."}, "savageSpinOutSpecial": {"name": "Savage Spin-Out", "effect": "Dummy <PERSON>"}, "neverEndingNightmarePhysical": {"name": "Never-Ending Nightmare", "effect": "Utilizando o Poder Z, o usuário invoca espectros rancorosos que sufocam o alvo. Seu poder varia dependendo do movimento original."}, "neverEndingNightmareSpecial": {"name": "Never-Ending Nightmare", "effect": "Dummy <PERSON>"}, "corkscrewCrashPhysical": {"name": "Corkscrew Crash", "effect": "Utilizando o Poder Z, o usuário gira rapidamente e ataca o alvo com força total. Seu poder varia dependendo do movimento original."}, "corkscrewCrashSpecial": {"name": "Corkscrew Crash", "effect": "Dummy <PERSON>"}, "infernoOverdrivePhysical": {"name": "Inferno Overdrive", "effect": "Utilizando o Poder Z, o usuário cospe uma enorme labareda ardente contra o alvo com força total. Seu poder varia dependendo do movimento original."}, "infernoOverdriveSpecial": {"name": "Inferno Overdrive", "effect": "Dummy <PERSON>"}, "hydroVortexPhysical": {"name": "Hydro Vortex", "effect": "Utilizando o Poder Z, o usuário cria um intenso turbilhão que engole o alvo com força total. Seu poder varia dependendo do movimento original."}, "hydroVortexSpecial": {"name": "Hydro Vortex", "effect": "Dummy <PERSON>"}, "bloomDoomPhysical": {"name": "<PERSON>", "effect": "Utilizando o Poder Z, o usuário absorve a energia vital do ambiente e ataca o alvo com força total. Seu poder varia dependendo do movimento original."}, "bloomDoomSpecial": {"name": "<PERSON>", "effect": "Dummy <PERSON>"}, "gigavoltHavocPhysical": {"name": "Gigavolt Havoc", "effect": "Utilizando o Poder Z, o usuário concentra uma corrente elétrica carregada e atinge o alvo. Seu poder varia dependendo do movimento original."}, "gigavoltHavocSpecial": {"name": "Gigavolt Havoc", "effect": "Dummy <PERSON>"}, "shatteredPsychePhysical": {"name": "Shattered Psyche", "effect": "Utilizando o Poder Z, o usuário manipula a cabeça do alvo e destrói-o mentalmente. Seu poder varia dependendo do movimento original."}, "shatteredPsycheSpecial": {"name": "Shattered Psyche", "effect": "Dummy <PERSON>"}, "subzeroSlammerPhysical": {"name": "Subzero Slammer", "effect": "Utilizando o Poder Z, o usuário lança um raio de gelo que reduz a temperatura do alvo a zero. Seu poder varia dependendo do movimento original."}, "subzeroSlammerSpecial": {"name": "Subzero Slammer", "effect": "Dummy <PERSON>"}, "devastatingDrakePhysical": {"name": "Devastating <PERSON>", "effect": "Utilizando o Poder Z, o usuário materializa sua aura, que ataca o alvo com força total. Seu poder varia dependendo do movimento original."}, "devastatingDrakeSpecial": {"name": "Devastating <PERSON>", "effect": "Dummy <PERSON>"}, "blackHoleEclipsePhysical": {"name": "Black Hole Eclipse", "effect": "Utilizando o Poder Z, o usuário cria um buraco negro que engole o alvo. Seu poder varia dependendo do movimento original."}, "blackHoleEclipseSpecial": {"name": "Black Hole Eclipse", "effect": "Dummy <PERSON>"}, "twinkleTacklePhysical": {"name": "Twinkle Tackle", "effect": "Utilizando o Poder Z, o usuário cria uma dimensão graciosa que deixa o alvo a sua mercê. Seu poder varia dependendo do movimento original."}, "twinkleTackleSpecial": {"name": "Twinkle Tackle", "effect": "Dummy <PERSON>"}, "catastropika": {"name": "Catastropika", "effect": "Utilizando seu Poder Z, <PERSON><PERSON><PERSON> acumula o máximo de eletricidade que seu corpo suporta e pula no alvo com força total."}, "shoreUp": {"name": "Shore Up", "effect": "Recupera metade dos PS do usuário. Durante uma Tempestade de Areia, recupera um pouco mais."}, "firstImpression": {"name": "First Impression", "effect": "Embora esse movimento seja poderoso, ele funciona apenas na primeira rodada em que o usuário está em batalha."}, "banefulBunker": {"name": "<PERSON>ef<PERSON> Bunker", "effect": "Além de proteger o usuário de ataques, este movimento também envenena qualquer um que fizer contato direto."}, "spiritShackle": {"name": "Spirit Shackle", "effect": "O usuário ataca enquanto fisga simultaneamente a sombra do alvo e impede ele de escapar."}, "darkestLariat": {"name": "Darkest Lariat", "effect": "O usuário balança os dois braços e acerta o alvo. As mudanças de atributos do alvo não afetam o dano deste ataque."}, "sparklingAria": {"name": "Sparkling Aria", "effect": "Libera borbulhas ao cantar. Se um Pokémon estiver queimado, ele será curado pelo toque das bolhas."}, "iceHammer": {"name": "Ice Hammer", "effect": "O usuário gira seu corpo e bate com seus fortes e pesados punhos. Isso diminui a Velocidade do usuário."}, "floralHealing": {"name": "<PERSON><PERSON>", "effect": "O usuário restaura os PS do alvo até metade a dos seus PS máximo. Ele restaura mais HP quando o terreno é de grama."}, "highHorsepower": {"name": "High Horsepower", "effect": "O usuário ataca ferozmente o alvo usando todo o seu corpo."}, "strengthSap": {"name": "Strength Sap", "effect": "O usuário restaura seus PS em uma quantidade igual ao atributo de Ataque do alvo. Também diminui o atributo de Ataque do alvo."}, "solarBlade": {"name": "Solar Blade", "effect": "O usuário absorve luz e concentra-a em forma de lâmina com a energia absorvida no primeiro turno e atacando o alvo no próximo turno."}, "leafage": {"name": "Leafage", "effect": "O usuário ataca lançando folhas no alvo."}, "spotlight": {"name": "Spotlight", "effect": "O usuário direciona o foco no alvo para que apenas ele seja atacado durante o turno."}, "toxicThread": {"name": "Toxic Thread", "effect": "O usuário dispara fios venenosos para envenenar o alvo e diminuir sua Velocidade."}, "laserFocus": {"name": "Laser Focus", "effect": "O usuário se concentra intensamente. O ataque no próximo turno sempre resultará em um acerto crítico."}, "gearUp": {"name": "Gear Up", "effect": "O usuário engata suas engrenagens para aumentar os atributos de Ataque e Ataque Esp. de Pokémon aliados com as Habilidades Mai<PERSON> ou Menos."}, "throatChop": {"name": "<PERSON><PERSON><PERSON>", "effect": "O usuário acerta a garganta do alvo, e a dor resultante impede que o alvo use movimentos que emitam som por dois turnos."}, "pollenPuff": {"name": "<PERSON><PERSON>", "effect": "O usuário ataca o inimigo com uma bola de pólen explosiva. Se o alvo for um aliado, ele recebe uma bola de pólen que restaura seus PS em vez disso."}, "anchorShot": {"name": "<PERSON><PERSON>", "effect": "O usuário revolve o alvo com a corrente de sua âncora enquanto ataca. O alvo se torna incapaz de fugir."}, "psychicTerrain": {"name": "Psychic Terrain", "effect": "Protege o Pokémon no terreno de movimentos de prioridade e aumenta o poder dos movimentos do tipo Psíquico por cinco turnos."}, "lunge": {"name": "<PERSON><PERSON>", "effect": "O usuário faz uma estocada no alvo, atacando com força total. Isso também diminui o Ataque do alvo."}, "fireLash": {"name": "Fire Lash", "effect": "O usuário atinge o alvo com um chicote em chamas. Também diminui a Defesa do alvo."}, "powerTrip": {"name": "Power Trip", "effect": "O usuário ostenta sua força e ataca o alvo. Quanto mais os atributos do usuário forem aumentados, maior será o poder do movimento."}, "burnUp": {"name": "Burn Up", "effect": "Para infligir um dano massivo, o usuário se exaure. Após usar esse movimento, o usuário não será mais do tipo Fogo."}, "speedSwap": {"name": "Speed Swap", "effect": "O usuário troca os atributos de Velocidade com o alvo."}, "smartStrike": {"name": "Smart Strike", "effect": "O usuário perfura o alvo com um chifre afiado. Esse ataque nunca erra."}, "purify": {"name": "Purify", "effect": "O usuário cura a condição de estado do alvo. Se o movimento for bem-sucedido, também recupera os PS do usuário."}, "revelationDance": {"name": "Revelation Dance", "effect": "O usuário ataca o alvo dançando intensamente. O tipo do usuário determina o tipo deste movimento."}, "coreEnforcer": {"name": "Core Enforcer", "effect": "Se os Pokémon nos quais o usuário causou dano já tiverem usado seus movimentos, esse movimento elimina o efeito da Habilidade do alvo."}, "tropKick": {"name": "Trop Kick", "effect": "O usuário desfere um chute intenso de origens tropicais no alvo. Também diminui o Ataque do alvo."}, "instruct": {"name": "Instruct", "effect": "O usuário instrui o alvo a usar novamente o seu último movimento usado."}, "beakBlast": {"name": "<PERSON><PERSON>", "effect": "O usuário aquece o bico e depois ataca o alvo. Fazer contato direto com o Pokémon enquanto ele aquece o bico resultará em queimadura."}, "clangingScales": {"name": "Clanging Scales", "effect": "O usuário esfrega as escamas de todo o seu corpo e faz um estrondo para machucar o Pokémon oponente. A Defesa do usuário diminui após o ataque."}, "dragonHammer": {"name": "Dragon Hammer", "effect": "O usuário usa seu corpo como um martelo para atacar o alvo e causar dano."}, "brutalSwing": {"name": "Brutal Swing", "effect": "O usuário balança o corpo violentamente para infligir dano a tudo em seu redor."}, "auroraVeil": {"name": "Aurora Veil", "effect": "Esse movimento reduz o dano de movimentos físicos e especiais por cinco turnos. Só pode ser usado durante uma tempestade de granizo."}, "sinisterArrowRaid": {"name": "Sinister Arrow Raid", "effect": "Utilizando o Poder Z, Decidueye cria incontáveis flechas e dispara-as contra o alvo com força total."}, "maliciousMoonsault": {"name": "Malicious <PERSON>", "effect": "Utilizando o Poder Z, Incineroar fortalece seu corpo e pula no alvo com força total."}, "oceanicOperetta": {"name": "Oceanic Operetta", "effect": "Utilizando o Poder Z, Primarina convoca uma quantidade massiva de água e lança contra o alvo com força total."}, "guardianOfAlola": {"name": "Guardian of Alola", "effect": "Utilizando o Poder Z, o Espírito Nativo canaliza a energia de Alola e ataca o alvo com força total. Reduz muito os PS do alvo."}, "soulStealing7StarStrike": {"name": "Soul-Stealing 7-Star Strike", "effect": "Quando um Marshadow obtém o Poder Z, ele ataca o alvo consecutivamente com socos e chutes usando força total."}, "stokedSparksurfer": {"name": "<PERSON>d <PERSON>", "effect": "Quando um Raichu de Alola obtém o Poder Z, ele lança um ataque contra o alvo com força total. Este movimento deixa o alvo paralisado."}, "pulverizingPancake": {"name": "Pulverizing Pancake", "effect": "O Poder Z desperta as capacidades máximas de seu Snorlax. O Pokémon movimenta seu enorme corpo velozmente e ataca o alvo com força total."}, "extremeEvoboost": {"name": "Extreme Evoboost", "effect": "Quando um Eevee obtém o Poder Z, ele absorve energia dos seus amigos evoluídos e aumenta os seus atributos bruscamente."}, "genesisSupernova": {"name": "Genesis Supernova", "effect": "Quando um Mew obtém o Poder Z, ele ataca o alvo com força total. O terreno será carregado com energia psíquica."}, "shellTrap": {"name": "Shell Trap", "effect": "O usuário arma uma cilada explosiva. Se o usuário for atingido fisicamente, a cilada irá explodir e causar de dano ao Pokémon oponente."}, "fleurCannon": {"name": "<PERSON><PERSON><PERSON>", "effect": "O usuário dispara um raio poderoso. O efeito colateral do ataque prejudica duramente o Ataque Especial do usuário."}, "psychicFangs": {"name": "Psychic Fangs", "effect": "O usuário morde o alvo com suas capacidades psíquicas. Pode destruir Tela de Luz e Refletir."}, "stompingTantrum": {"name": "Stomping <PERSON>", "effect": "<PERSON><PERSON><PERSON> pela frustração, o usuário ataca o alvo. Se o movimento anterior falhou, o poder do movimento é dobrado."}, "shadowBone": {"name": "Shadow Bone", "effect": "O usuário ataca o alvo com um osso que contém um espírito. Pode diminuir a Defesa do alvo."}, "accelerock": {"name": "Accelerock", "effect": "O usuário colide contra o alvo em alta velocidade. Esse movimento sempre ataca primeiro."}, "liquidation": {"name": "Liquidation", "effect": "O usuário dispara no alvo um jato d’água poderoso. Diminui a Defesa do alvo."}, "prismaticLaser": {"name": "Prismatic Laser", "effect": "O usuário dispara lasers poderosos usando o poder de um prisma. O usuário não pode se mover no próximo turno."}, "spectralThief": {"name": "Spectral Thief", "effect": "O usuário se esconde na sombra do alvo, rouba seus aumentos de atributos e então, ataca-o."}, "sunsteelStrike": {"name": "Sunsteel Strike", "effect": "O usuário atinge o alvo com a força de um meteoro. Esse movimento pode ser usado no alvo independentemente de sua Habilidade."}, "moongeistBeam": {"name": "Moongeist <PERSON><PERSON>", "effect": "O usuário emite um raio pavoroso para atacar o alvo. Esse movimento pode ser usado no alvo independentemente de sua Habilidade."}, "tearfulLook": {"name": "Tearful Look", "effect": "O usuário fica manhoso e o alvo perde a vontade de lutar. Diminui o Ataque e o Ataque Esp. do alvo."}, "zingZap": {"name": "<PERSON><PERSON>", "effect": "Uma forte explosão elétrica que cai sobre o alvo, eletrocutando-o e podendo fazê-lo hesitar."}, "naturesMadness": {"name": "Nature’s Madness", "effect": "O usuário atinge o alvo com a força da natureza. Reduz os PS do alvo pela metade."}, "multiAttack": {"name": "Multi-Attack", "effect": "Se envolvendo em energia concentrada, o usuário acerta o alvo. A memória segurada determina o tipo do movimento."}, "tenMillionVoltThunderbolt": {"name": "10,000,000 Volt Thunderbolt", "effect": "<PERSON>ando seu Poder Z, o Pikachu de boné acumula eletricidade e despeja-a. <PERSON> críticos acertam mais facilmente."}, "mindBlown": {"name": "Mind Blown", "effect": "O usuário ataca tudo ao seu redor fazendo sua própria cabeça explodir. <PERSON><PERSON> também causa dano ao usuário."}, "plasmaFists": {"name": "Plasma Fists", "effect": "O usuário ataca com punhos carregados eletricamente. Este movimento transforma movimentos do tipo Normal em movimentos do tipo Elétrico."}, "photonGeyser": {"name": "<PERSON><PERSON>", "effect": "O usuário ataca o alvo com um pilar de luz. Este movimento causa dano de Ataque ou Ataque Especial—o que for maior para o usuário."}, "lightThatBurnsTheSky": {"name": "Light That Burns the Sky", "effect": "Este ataque causa dano de Ataque ou Ataque Especial—o que for maior para o usuário, Necrozma. Este movimento ignora a Habilidade do alvo."}, "searingSunrazeSmash": {"name": "Searing Sunraze Smash", "effect": "Após obter o Z-Power, o usuário, Solgaleo, ataca o alvo com força total. Este movimento pode ignorar o efeito da Habilidade do alvo."}, "menacingMoonrazeMaelstrom": {"name": "Menacing <PERSON><PERSON>", "effect": "Após obter o Z-Power, o usu<PERSON>rio, <PERSON><PERSON>, ataca o alvo com força total. Este movimento pode ignorar o efeito da Habilidade do alvo."}, "letsSnuggleForever": {"name": "Let’s Snuggle Forever", "effect": "Após obter o Z-Power, o usuário, Mimikyu, soca o alvo com força total."}, "splinteredStormshards": {"name": "Splintered Stormshards", "effect": "Após obter o Z-Power, o usuário, Lycanroc, ataca o alvo com força total. Este movimento nega o efeito no campo de batalha."}, "clangorousSoulblaze": {"name": "Clangorous Soulblaze", "effect": "Após obter o Z-Power, o usu<PERSON>rio, <PERSON><PERSON><PERSON><PERSON>o, ataca os Pokémon adversários com força total. Este movimento aumenta os atributos do usuário."}, "zippyZap": {"name": "<PERSON><PERSON><PERSON>", "effect": "O usuário ataca o alvo com rajadas de eletricidade em alta velocidade. Esse movimento sempre vai primeiro e resulta em um golpe crítico."}, "splishySplash": {"name": "Splishy Splash", "effect": "O usuário carrega uma onda enorme com eletricidade e atinge os Pokémon adversários com a onda. <PERSON><PERSON> também pode deixar os Pokémon adversários paralisados."}, "floatyFall": {"name": "Floaty Fall", "effect": "O usuário flutua no ar e então mergulha em um ângulo íngreme para atacar o alvo. <PERSON><PERSON> também pode fazer o alvo hesitar."}, "pikaPapow": {"name": "<PERSON><PERSON>", "effect": "Quanto mais o Pikachu ama seu Treinador, maior o poder do movimento. Nunca erra."}, "bouncyBubble": {"name": "<PERSON><PERSON><PERSON>", "effect": "O usuário dispara bolhas de água contra o alvo. Em seguida, absorve água e restaura seus PS na quantidade de dano sofrido pelo alvo."}, "buzzyBuzz": {"name": "<PERSON><PERSON>", "effect": "O usuário dispara um choque de eletricidade para atacar o alvo. <PERSON><PERSON> também deixa o alvo paralisado."}, "sizzlySlide": {"name": "Sizzly Slide", "effect": "O usuário se envolve em fogo e carrega contra o alvo. <PERSON><PERSON> também deixa o alvo queimado."}, "glitzyGlow": {"name": "Glitzy Glow", "effect": "O usuário bombardeia o alvo com força telecinética. Uma parede maravilhosa de luz é erguida para enfraquecer o poder dos movimentos especiais dos Pokémon adversários."}, "baddyBad": {"name": "Baddy Bad", "effect": "O usuário age mal e ataca o alvo. Uma parede maravilhosa de luz é erguida para enfraquecer o poder dos movimentos físicos dos Pokémon adversários."}, "sappySeed": {"name": "Sappy Seed", "effect": "O usuário cresce um caule gigantesco que espalha sementes para atacar o alvo. As sementes drenam o HP do alvo a cada turno."}, "freezyFrost": {"name": "<PERSON><PERSON>", "effect": "O usuário ataca com um cristal feito de névoa congelada fria. <PERSON><PERSON> elimina to<PERSON> as mudanças de atributo entre todos os Pokémon envolvidos na batalha."}, "sparklySwirl": {"name": "Sparkly Swirl", "effect": "O usuário ataca o alvo envolvendo-o com um redemoinho de um aroma esmagador. <PERSON><PERSON> também cura todas as condições de status do grupo do usuário."}, "veeveeVolley": {"name": "<PERSON><PERSON><PERSON><PERSON>", "effect": "Quanto mais o Eevee ama seu Treinador, maior o poder do movimento. Nunca erra."}, "doubleIronBash": {"name": "Double Iron Bash", "effect": "O usuário gira, centrando a porca hexagonal em seu peito e depois ataca com seus braços duas vezes seguidas. <PERSON><PERSON> também pode fazer o alvo hesitar."}, "maxGuard": {"name": "Max Guard", "effect": "Este movimento permite ao usuário proteger-se de todos os ataques. Sua chance de falhar aumenta se for usado em sucessão."}, "dynamaxCannon": {"name": "Dynamax Cannon", "effect": "O usuário libera um forte feixe de seu núcleo. Este movimento causa até o dobro do dano se o alvo estiver com seu nível acima do limite."}, "snipeShot": {"name": "<PERSON><PERSON><PERSON>", "effect": "O usuário ignora os efeitos dos movimentos e Habilidades dos Pokémon adversários que atraem movimentos, permitindo que este movimento atinja o alvo escolhido."}, "jawLock": {"name": "Jaw Lock", "effect": "Este movimento impede o usuário e o alvo de trocarem de lugar até que um deles desmaie. O efeito desaparece se qualquer um dos Pokémon deixar o campo de batalha."}, "stuffCheeks": {"name": "Stuff Cheeks", "effect": "O usuário come sua Fruta segurada, depois aumenta muito seu atributo de Defesa."}, "noRetreat": {"name": "No Retreat", "effect": "Este movimento aumenta todos os atributos do usuário, mas impede o usuário de trocar de lugar ou fugir."}, "tarShot": {"name": "Tar Shot", "effect": "O usuário derrama alcatrão pegajoso sobre o alvo, diminuindo o atributo de Velocidade do alvo. O alvo se torna mais fraco contra movimentos do tipo Fogo."}, "magicPowder": {"name": "Magic Powder", "effect": "O usuário espalha uma nuvem de pó mágico que muda o alvo para o tipo Psíquico."}, "dragonDarts": {"name": "Dragon Darts", "effect": "O usuário ataca duas vezes usando Dreepy. Se houver dois alvos, este movimento atinge cada alvo uma vez."}, "teatime": {"name": "Teatime", "effect": "O usuário faz hora do chá com todos os Pokémon na batalha. Cada Pokémon come sua Fruta segurada."}, "octolock": {"name": "Octolock", "effect": "O usuário prende o alvo e impede que ele fuja. Este movimento também diminui os atributos de Defesa e Def. Esp. do alvo a cada turno."}, "boltBeak": {"name": "<PERSON><PERSON>", "effect": "O usuário fere o alvo com seu bico eletrificado. Se o usuário atacar antes do alvo, o poder deste movimento é dobrado."}, "fishiousRend": {"name": "<PERSON>ious Rend", "effect": "O usuário fere o alvo com suas brânquias duras. Se o usuário atacar antes do alvo, o poder deste movimento é dobrado."}, "courtChange": {"name": "Court Change", "effect": "Com seu poder misterioso, o usuário troca os efeitos de cada lado do campo de batalha."}, "maxFlare": {"name": "<PERSON>", "effect": "Este é um ataque do tipo Fogo que Pokémon Dynamax usam. O usuário intensifica o sol por cinco turnos."}, "maxFlutterby": {"name": "<PERSON>", "effect": "Este é um ataque do tipo Inseto que Pokémon Dynamax usam. <PERSON><PERSON> diminui o atributo de Atq. Esp. do alvo."}, "maxLightning": {"name": "Max Lightning", "effect": "Este é um ataque do tipo Elétrico que Pokémon Dynamax usam. O usuário transforma o chão em Terreno Elétrico por cinco turnos."}, "maxStrike": {"name": "Max Strike", "effect": "Este é um ataque do tipo Normal que Pokémon Dynamax usam. <PERSON><PERSON> diminui o atributo de Velocidade do alvo."}, "maxKnuckle": {"name": "<PERSON>", "effect": "Este é um ataque do tipo Lutador que Pokémon Dynamax usam. Isso aumenta os atributos de Ataque dos Pokémon aliados."}, "maxPhantasm": {"name": "Max Phantasm", "effect": "Este é um ataque do tipo Fantasma que Pokémon Dynamax usam. <PERSON><PERSON> diminui o atributo de Defesa do alvo."}, "maxHailstorm": {"name": "<PERSON>", "effect": "Este é um ataque do tipo Gelo que Pokémon Dynamax usam. O usuário convoca uma tempestade de granizo que dura cinco turnos."}, "maxOoze": {"name": "<PERSON>", "effect": "Este é um ataque do tipo Veneno que Pokémon Dynamax usam. Isso aumenta os atributos de Atq. Esp. dos Pokémon aliados."}, "maxGeyser": {"name": "<PERSON>", "effect": "Este é um ataque do tipo Água que Pokémon Dynamax usam. O usuário convoca uma chuva pesada que cai por cinco turnos."}, "maxAirstream": {"name": "Max Airstream", "effect": "Este é um ataque do tipo Voador que Pokémon Dynamax usam. Isso aumenta os atributos de Velocidade dos Pokémon aliados."}, "maxStarfall": {"name": "<PERSON>", "effect": "Este é um ataque do tipo Fada que Pokémon Dynamax usam. O usuário transforma o chão em Terreno de Nevoeiro por cinco turnos."}, "maxWyrmwind": {"name": "<PERSON>", "effect": "Este é um ataque do tipo Dragão que Pokémon Dynamax usam. <PERSON><PERSON> diminui o atributo de Ataque do alvo."}, "maxMindstorm": {"name": "<PERSON>", "effect": "Este é um ataque do tipo Psíquico que Pokémon Dynamax usam. O usuário transforma o chão em Terreno Psíquico por cinco turnos."}, "maxRockfall": {"name": "<PERSON>", "effect": "Este é um ataque do tipo Pedra que Pokémon Dynamax usam. O usuário convoca uma tempestade de areia que dura cinco turnos."}, "maxQuake": {"name": "<PERSON>", "effect": "Este é um ataque do tipo Terra que Pokémon Dynamax usam. Isso aumenta os atributos de Def. Esp. dos Pokémon aliados."}, "maxDarkness": {"name": "Max Darkness", "effect": "Este é um ataque do tipo Sombrio que Pokémon Dynamax usam. <PERSON><PERSON> diminui o atributo de Def. Esp. do alvo."}, "maxOvergrowth": {"name": "Max Overgrowth", "effect": "Este é um ataque do tipo Grama que Pokémon Dynamax usam. O usuário transforma o chão em Terreno de Grama por cinco turnos."}, "maxSteelspike": {"name": "<PERSON> Steel<PERSON>", "effect": "Este é um ataque do tipo Aço que Pokémon Dynamax usam. Isso aumenta os atributos de Defesa dos Pokémon aliados."}, "clangorousSoul": {"name": "Clangorous Soul", "effect": "O usuário aumenta todos os seus atributos usando um pouco de seu HP."}, "bodyPress": {"name": "Body Press", "effect": "O usuário ataca pressionando seu corpo contra o alvo. Quanto maior a Defesa do usuário, mais dano pode infligir ao alvo."}, "decorate": {"name": "Decorate", "effect": "O usuário aumenta muito os atributos de Ataque e Atq. Esp. do alvo decorando o alvo."}, "drumBeating": {"name": "Drum Beating", "effect": "O usuário toca seu tambor, controlando as raízes do tambor para atacar o alvo. Isso também diminui o atributo de Velocidade do alvo."}, "snapTrap": {"name": "Snap Trap", "effect": "O usuário prende o alvo em uma armadilha rápida por quatro ou cinco turnos."}, "pyroBall": {"name": "Pyro <PERSON>", "effect": "O usuário ataca acendendo uma pequena pedra e lançando-a como uma bola de fogo no alvo. <PERSON><PERSON> também pode deixar o alvo queimado."}, "behemothBlade": {"name": "Behemoth Blade", "effect": "O usuário empunha uma espada grande e poderosa usando todo o seu corpo e corta o alvo em um ataque vigoroso."}, "behemothBash": {"name": "Behemoth Bash", "effect": "O corpo do usuário se torna um escudo firme e atinge o alvo com força."}, "auraWheel": {"name": "Aura Wheel", "effect": "O usuário ataca e aumenta sua Velocidade com a energia armazenada em suas bochechas. Se usado por Morpeko, o tipo deste movimento muda dependendo da forma do usuário."}, "breakingSwipe": {"name": "Breaking Swipe", "effect": "O usuário balança sua cauda dura violentamente e ataca os Pokémon adversários. Isso também diminui os atributos de Ataque deles."}, "branchPoke": {"name": "Branch Poke", "effect": "O usuário ataca o alvo cutucando-o com um galho pontiagudo."}, "overdrive": {"name": "Overdrive", "effect": "O usuário ataca os Pokémon adversários vibrando uma guitarra ou baixo, causando um eco enorme e uma vibração forte."}, "appleAcid": {"name": "Apple Acid", "effect": "O usuário ataca o alvo com um líquido ácido criado a partir de maçãs azedas. Isso também diminui o atributo de Def. Esp. do alvo."}, "gravApple": {"name": "Grav Apple", "effect": "O usuário inflige dano derrubando uma maçã de cima. <PERSON><PERSON> também diminui o atributo de Defesa do alvo."}, "spiritBreak": {"name": "Spirit Break", "effect": "O usuário ataca o alvo com tanta força que poderia quebrar o espírito do alvo. Isso também diminui o atributo de Atq. Esp. do alvo."}, "strangeSteam": {"name": "Strange Steam", "effect": "O usuário ataca o alvo emitindo vapor. <PERSON><PERSON> também pode deixar o alvo confuso."}, "lifeDew": {"name": "Life Dew", "effect": "O usuário espalha água misteriosa ao redor e restaura o HP de si mesmo e de seus Pokémon aliados na batalha."}, "obstruct": {"name": "Obstruct", "effect": "Este movimento permite ao usuário proteger-se de todos os ataques. Sua chance de falhar aumenta se for usado em sucessão. Contato direto reduz severamente o atributo de Defesa do atacante."}, "falseSurrender": {"name": "False Surrender", "effect": "O usuário finge abaixar a cabeça, mas então esfaqueia o alvo com seus cabelos desgrenhados. Este ataque nunca erra."}, "meteorAssault": {"name": "Meteor Assault", "effect": "O usuário ataca selvagemente com seu alho-poró grosso. O usuário não pode se mover na próxima rodada, porque a força deste movimento o faz cambalear."}, "eternabeam": {"name": "Eternabeam", "effect": "Este é o ataque mais poderoso de Eternatus em sua forma original. O usuário não pode se mover na próxima rodada."}, "steelBeam": {"name": "Steel Beam", "effect": "O usuário dispara um feixe de aço que coletou de todo o seu corpo. <PERSON><PERSON> também causa dano ao usuário."}, "expandingForce": {"name": "Expanding Force", "effect": "O usuário ataca o alvo com seu poder psíquico. O poder deste movimento aumenta e danifica todos os Pokémon adversários no Terreno Psíquico."}, "steelRoller": {"name": "Steel Roller", "effect": "O usuário ataca enquanto destrói o terreno. Este movimento falha quando o chão não foi transformado em um terreno."}, "scaleShot": {"name": "Scale Shot", "effect": "O usuário ataca atirando escamas de duas a cinco vezes seguidas. Este movimento aumenta o atributo de Velocidade do usuário, mas diminui seu atributo de Defesa."}, "meteorBeam": {"name": "Met<PERSON> Beam", "effect": "Neste ataque de dois turnos, o usuário reúne energia espacial e aumenta seu atributo de Atq. Esp., depois ataca o alvo no próximo turno."}, "shellSideArm": {"name": "Shell Side Arm", "effect": "Este movimento causa dano físico ou especial, o que for mais eficaz. <PERSON><PERSON> também pode envenenar o alvo."}, "mistyExplosion": {"name": "Misty Explosion", "effect": "O usuário ataca tudo ao seu redor e desmaia ao usar este movimento. O poder deste movimento é aumentado no Terreno de Nevoeiro."}, "grassyGlide": {"name": "Grassy Glide", "effect": "Deslizando no chão, o usuário ataca o alvo. Este movimento sempre ataca primeiro no Terreno de Grama."}, "risingVoltage": {"name": "Rising Voltage", "effect": "O usuário ataca com a voltagem elétrica que sobe do chão. O poder deste movimento dobra quando o alvo está no Terreno Elétrico."}, "terrainPulse": {"name": "Terrain Pulse", "effect": "O usuário utiliza o poder do terreno para atacar. O tipo e o poder deste movimento mudam dependendo do terreno em que é usado."}, "skitterSmack": {"name": "Skitter Smack", "effect": "O usuário corre por trás do alvo para atacar. <PERSON><PERSON> também diminui o atributo de Atq. Esp. do alvo."}, "burningJealousy": {"name": "Burning Jealousy", "effect": "O usuário ataca com energia da inveja. Isso deixa todos os Pokémon adversários que tiveram seus atributos aumentados durante o turno com uma queimadura."}, "lashOut": {"name": "Lash Out", "effect": "O usuário ataca para desabafar sua frustração contra o alvo. Se os atributos do usuário foram diminuídos durante este turno, o poder deste movimento é dobrado."}, "poltergeist": {"name": "Poltergeist", "effect": "O usuário ataca o alvo controlando o item do alvo. O movimento falha se o alvo não tiver um item."}, "corrosiveGas": {"name": "Corrosive Gas", "effect": "O usuário envolve tudo ao seu redor com gás altamente ácido e derrete os itens que eles seguram."}, "coaching": {"name": "Coaching", "effect": "O usuário treina adequadamente seus Pokémon aliados, aumentando seus atributos de Ataque e Defesa."}, "flipTurn": {"name": "Flip Turn", "effect": "Após fazer seu ataque, o usuário corre para trocar de lugar com um Pokémon do grupo à espera."}, "tripleAxel": {"name": "Triple Axel", "effect": "Um ataque de três chutes consecutivos que se torna mais poderoso a cada acerto bem-sucedido."}, "dualWingbeat": {"name": "Dual Wingbeat", "effect": "O usuário atinge o alvo com suas asas. O alvo é atingido duas vezes seguidas."}, "scorchingSands": {"name": "Scorching Sands", "effect": "O usuário joga areia escaldante no alvo para atacar. <PERSON><PERSON> tamb<PERSON>m pode deixar o alvo queimado."}, "jungleHealing": {"name": "Jungle Healing", "effect": "O usuário se torna um com a selva, restaurando HP e curando quaisquer condições de status de si mesmo e de seus Pokémon aliados na batalha."}, "wickedBlow": {"name": "Wicked Blow", "effect": "O usuário, tendo dominado o estilo Sombrio, atinge o alvo com um golpe feroz. Este ataque sempre resulta em um acerto crítico."}, "surgingStrikes": {"name": "Surging Strikes", "effect": "O usuário, tendo dominado o estilo <PERSON>, atinge o alvo com um movimento fluido três vezes seguidas. Estes ataques sempre resultam em acertos críticos."}, "thunderCage": {"name": "Thunder Cage", "effect": "O usuário prende o alvo em uma gaiola de eletricidade cintilante por quatro ou cinco turnos."}, "dragonEnergy": {"name": "Dragon Energy", "effect": "Convertendo sua força vital em poder, o usuário ataca os Pokémon adversários. Quanto menor o HP do usuário, menor o poder do movimento."}, "freezingGlare": {"name": "Freezing Glare", "effect": "O usuário dispara seu poder psíquico dos olhos para atacar. <PERSON><PERSON> também pode deixar o alvo congelado."}, "fieryWrath": {"name": "<PERSON>ery <PERSON>", "effect": "O usuário transforma sua ira em uma aura semelhante ao fogo para atacar. <PERSON><PERSON> também pode fazer os Pokémon adversários hesitarem."}, "thunderousKick": {"name": "Thunderous Kick", "effect": "O usuário oprime o alvo com movimento semelhante ao relâmpago antes de entregar um chute. <PERSON><PERSON> também diminui o atributo de Defesa do alvo."}, "glacialLance": {"name": "Glacial Lance", "effect": "O usuário ataca lançando uma lança de gelo envolta em nevasca nos Pokémon adversários."}, "astralBarrage": {"name": "Astral Barrage", "effect": "O usuário ataca enviando uma quantidade assustadora de pequenos fantasmas nos Pokémon adversários."}, "eerieSpell": {"name": "Eerie Spell", "effect": "O usuário ataca com seu tremendo poder psíquico. <PERSON><PERSON> remove 3 PP do último movimento usado pelo alvo."}, "direClaw": {"name": "<PERSON><PERSON>", "effect": "O usuário ataca o alvo com garras destruidoras. <PERSON><PERSON> ta<PERSON>m pode deixar o alvo envenenado, paralisado ou adormecido."}, "psyshieldBash": {"name": "Psyshield Bash", "effect": "Envoltando-se em energia psíquica, o usuário se choca contra o alvo. Isso também aumenta o atributo de Defesa do usuário."}, "powerShift": {"name": "Power Shift", "effect": "O usuário troca seus atributos de Ataque e Defesa."}, "stoneAxe": {"name": "Stone Axe", "effect": "O usuário balança seus machados de pedra no alvo. Fragmentos de pedra deixados para trás por este ataque flutuam ao redor do alvo."}, "springtideStorm": {"name": "Springtide Storm", "effect": "O usuário ataca envolvendo os Pokémon adversários em ventos ferozes repletos de amor e ódio. <PERSON><PERSON> também pode diminuir os atributos de Ataque deles."}, "mysticalPower": {"name": "Mystical Power", "effect": "O usuário ataca emitindo um poder misterioso. Isso também aumenta o atributo de Atq. Esp. do usuário."}, "ragingFury": {"name": "Raging Fury", "effect": "O usuário se enfurece espalhando chamas por dois ou três turnos. O usuário então fica confuso."}, "waveCrash": {"name": "Wave Crash", "effect": "O usuário se envolve em água e atinge o alvo com todo o corpo para infligir dano. <PERSON><PERSON> também causa muito dano ao usuário."}, "chloroblast": {"name": "Chloroblast", "effect": "O usuário lança sua clorofila acumulada para infligir dano no alvo. <PERSON>so também causa dano ao usuário."}, "mountainGale": {"name": "Mountain Gale", "effect": "O usuário arremessa pedaços gigantes de gelo no alvo para infligir dano. <PERSON><PERSON> também pode fazer o alvo hesitar."}, "victoryDance": {"name": "Victory Dance", "effect": "O usuário realiza uma dança intensa para inaugurar a vitória, aumentando seus atributos de Ataque, Defesa e Velocidade."}, "headlongRush": {"name": "Headlong Rush", "effect": "O usuário se choca contra o alvo em um ataque de corpo inteiro. Isso também diminui os atributos de Defesa e Def. Esp. do usuário."}, "barbBarrage": {"name": "<PERSON><PERSON>", "effect": "O usuário lança inúmeras barbas tóxicas para infligir dano. O poder deste movimento é dobrado se o alvo já estiver envenenado."}, "esperWing": {"name": "<PERSON><PERSON>", "effect": "O usuário corta o alvo com asas enriquecidas com aura. <PERSON><PERSON> também aumenta o atributo de Velocidade do usuário. Este movimento tem uma chance aumentada de causar um acerto crítico."}, "bitterMalice": {"name": "Bitter Malice", "effect": "O usuário ataca o alvo com um ressentimento arrepiante. <PERSON><PERSON> também diminui o atributo de Ataque do alvo."}, "shelter": {"name": "Shelter", "effect": "O usuário torna sua pele tão dura quanto um escudo de ferro, aumentando muito seu atributo de Defesa."}, "tripleArrows": {"name": "Triple Arrows", "effect": "O usuário chuta e depois dispara três flechas. Este movimento tem uma chance aumentada de causar um acerto crítico e também pode diminuir o atributo de Defesa do alvo ou fazê-lo hesitar."}, "infernalParade": {"name": "Infernal Parade", "effect": "O usuário ataca com miríades de bolas de fogo. <PERSON><PERSON> tamb<PERSON>m pode deixar o alvo queimado. O poder deste movimento é dobrado se o alvo tiver uma condição de status."}, "ceaselessEdge": {"name": "Ceaseless Edge", "effect": "O usuário corta sua lâmina de concha no alvo. Fragmentos de concha deixados para trás por este ataque permanecem espalhados sob o alvo como espinhos."}, "bleakwindStorm": {"name": "Bleakwind Storm", "effect": "O usuário ataca com ventos selvagemente frios que fazem tanto o corpo quanto o espírito tremerem. <PERSON><PERSON> também pode diminuir os atributos de Velocidade dos Pokémon adversários."}, "wildboltStorm": {"name": "Wildbolt Storm", "effect": "O usuário invoca uma tempestade trovejante e ataca selvagemente com relâmpagos e vento. <PERSON><PERSON> tamb<PERSON>m pode deixar os Pokémon adversários paralisados."}, "sandsearStorm": {"name": "Sandsear Storm", "effect": "O usuário ataca envolvendo os Pokémon adversários em ventos ferozes e areia escaldante. <PERSON><PERSON> também pode deixá-los queimados."}, "lunarBlessing": {"name": "Lunar Blessing", "effect": "O usuário recebe uma bênção do crescente lunar, restaurando HP e curando condições de status para si mesmo e seus Pokémon aliados atualmente na batalha."}, "takeHeart": {"name": "Take Heart", "effect": "O usuário levanta o espírito, curando suas próprias condições de status e aumentando seus atributos de Atq. Esp. e Def. Esp."}, "gMaxWildfire": {"name": "G-Max Wildfire", "effect": "Um ataque do tipo Fogo que o Gigantamax Charizard usa. Este movimento continua causando dano aos oponentes por quatro turnos."}, "gMaxBefuddle": {"name": "<PERSON>-<PERSON>", "effect": "Um ataque do tipo Inseto que o Gigantamax Butterfree usa. Este movimento inflige as condições de envenenado, paralisado ou adormecido nos oponentes."}, "gMaxVoltCrash": {"name": "G-Max Volt Crash", "effect": "Um ataque do tipo Elétrico que o Gigantamax Pikachu usa. Este movimento paralisa os oponentes."}, "gMaxGoldRush": {"name": "G-Max Gold Rush", "effect": "Um ataque do tipo Normal que o Gigantamax Meowth usa. Este movimento confunde os oponentes e também ganha dinheiro extra."}, "gMaxChiStrike": {"name": "G-Max <PERSON>", "effect": "Um ataque do tipo Lutador que Gigantamax Machamp usa. Este movimento aumenta a chance de acertos críticos."}, "gMaxTerror": {"name": "G-Max Terror", "effect": "Um ataque do tipo Fantasma que Gigantamax Gengar usa. Este Pokémon pisa na sombra do Pokémon adversário para impedi-lo de escapar."}, "gMaxResonance": {"name": "G-Max Resonance", "effect": "Um ataque do tipo Gelo que Gigantamax Lapras usa. Este movimento reduz o dano recebido por cinco turnos."}, "gMaxCuddle": {"name": "<PERSON><PERSON><PERSON>", "effect": "Um ataque do tipo Normal que Gigantamax Eevee usa. Este movimento apaixona os oponentes."}, "gMaxReplenish": {"name": "<PERSON>-<PERSON>", "effect": "Um ataque do tipo Normal que Gigantamax Snorlax usa. Este movimento restaura Frutas que foram comidas."}, "gMaxMalodor": {"name": "<PERSON><PERSON><PERSON>", "effect": "Um ataque do tipo Veneno que Gigantamax Garbodor usa. Este movimento envenena os oponentes."}, "gMaxStonesurge": {"name": "G-<PERSON>", "effect": "Um ataque do tipo Água que Gigantamax Drednaw usa. Este movimento espalha pedras afiadas pelo campo de batalha."}, "gMaxWindRage": {"name": "G-Max Wind Rage", "effect": "Um ataque do tipo Voador que Gigantamax Corviknight usa. Este movimento remove os efeitos de movimentos como Reflect e Light Screen."}, "gMaxStunShock": {"name": "G-<PERSON>", "effect": "Um ataque do tipo Elétrico que Gigantamax Toxtricity usa. Este movimento envenena ou paralisa os oponentes."}, "gMaxFinale": {"name": "G-Max Finale", "effect": "Um ataque do tipo Fada que Gigantamax Alcremie usa. Este movimento cura os PS dos aliados."}, "gMaxDepletion": {"name": "G-Max Depletion", "effect": "Um ataque do tipo Dragão que Gigantamax Duraludon usa. Reduz o PP do último movimento usado."}, "gMaxGravitas": {"name": "G-<PERSON>", "effect": "Um ataque do tipo Psíquico que Gigantamax Orbeetle usa. Este movimento muda a gravidade por cinco turnos."}, "gMaxVolcalith": {"name": "<PERSON><PERSON><PERSON>", "effect": "Um ataque do tipo Pedra que Gigantamax Coalossal usa. Este movimento continua a causar dano aos oponentes por quatro turnos."}, "gMaxSandblast": {"name": "G-Max Sandblast", "effect": "Um ataque do tipo Terra que Gigantamax Sandaconda usa. Os oponentes ficam presos em uma tempestade de areia furiosa por quatro a cinco turnos."}, "gMaxSnooze": {"name": "G-<PERSON>", "effect": "Um ataque do tipo Sombrio que Gigantamax Grimmsnarl usa. O usuário solta um grande bocejo que faz com que os alvos adormeçam no próximo turno."}, "gMaxTartness": {"name": "<PERSON>-<PERSON>", "effect": "Um ataque do tipo Planta que Gigantamax Flapple usa. Este movimento reduz a Evasão dos oponentes."}, "gMaxSweetness": {"name": "G-<PERSON>", "effect": "Um ataque do tipo Planta que Gigantamax Appletun usa. Este movimento cura as condições de status dos aliados."}, "gMaxSmite": {"name": "G-Max Smite", "effect": "Um ataque do tipo Fada que Gigantamax Hatterene usa. Este movimento confunde os oponentes."}, "gMaxSteelsurge": {"name": "G-Max <PERSON>", "effect": "Um ataque do tipo Aço que Gigantamax Copperajah usa. Este movimento espalha estacas afiadas pelo campo de batalha."}, "gMaxMeltdown": {"name": "<PERSON>-<PERSON>", "effect": "Um ataque do tipo Aço que Gigantamax Melmetal usa. Este movimento impede os oponentes de usar o mesmo movimento duas vezes seguidas."}, "gMaxFoamBurst": {"name": "G-Max Foam Burst", "effect": "Um ataque do tipo Água que Gigantamax Kingler usa. Este movimento reduz drasticamente a Velocidade dos oponentes."}, "gMaxCentiferno": {"name": "G-Max <PERSON>", "effect": "Um ataque do tipo Fogo que Gigantamax Centiskorch usa. Este movimento prende os oponentes em chamas por quatro a cinco turnos."}, "gMaxVineLash": {"name": "G-Max <PERSON>", "effect": "Um ataque do tipo Planta que Gigantamax Venusaur usa. Este movimento continua a causar dano aos oponentes por quatro turnos."}, "gMaxCannonade": {"name": "G-Max Cannonade", "effect": "Um ataque do tipo Água que Gigantamax Blastoise usa. Este movimento continua a causar dano aos oponentes por quatro turnos."}, "gMaxDrumSolo": {"name": "G-Max Drum Solo", "effect": "Um ataque do tipo Planta que Gigantamax Rillaboom usa. Este movimento pode ser usado no alvo independentemente de suas Habilidades."}, "gMaxFireball": {"name": "G-Max Fireball", "effect": "Um ataque do tipo Fogo que Gigantamax Cinderace usa. Este movimento pode ser usado no alvo independentemente de suas Habilidades."}, "gMaxHydrosnipe": {"name": "G-Max Hydrosnipe", "effect": "Um ataque do tipo Água que Gigantamax Inteleon usa. Este movimento pode ser usado no alvo independentemente de suas Habilidades."}, "gMaxOneBlow": {"name": "G-Max One Blow", "effect": "Um ataque do tipo Sombrio que Gigantamax Urshifu usa. Este movimento único pode ignorar o Max Guard."}, "gMaxRapidFlow": {"name": "G-Max Rapid Flow", "effect": "Um ataque do tipo Água que Gigantamax Urshifu usa. Este movimento rápido pode ignorar o Max Guard."}, "teraBlast": {"name": "<PERSON><PERSON>", "effect": "Se o usuário estiver Terastalizado, ele libera energia de seu Tera Tipo. Este movimento causa dano usando o maior entre o Ataque ou Ataque Esp. do usuário."}, "silkTrap": {"name": "Silk Trap", "effect": "O usuário tece uma armadilha de seda, protegendo-se de dano enquanto reduz o atributo de Velocidade de qualquer atacante que faça contato direto."}, "axeKick": {"name": "Axe Kick", "effect": "O usuário ataca chutando para cima e depois abaixando o calcanhar sobre o alvo. <PERSON><PERSON> também pode confundir o alvo. Se errar, o usuário sofre dano."}, "lastRespects": {"name": "Last Respects", "effect": "O usuário ataca para vingar seus aliados. Quanto mais aliados derrotados, maior o poder do movimento."}, "luminaCrash": {"name": "<PERSON><PERSON>", "effect": "O usuário ataca liberando uma luz peculiar que afeta até a mente. Isso também reduz muito a Defesa Esp. do alvo."}, "orderUp": {"name": "Order Up", "effect": "O usuário ataca com elegância. Se o usuário tiver um Tatsugiri na boca, este movimento aumenta uma dos atributos do usuário com base na forma do Tatsugiri."}, "jetPunch": {"name": "Jet Punch", "effect": "O usuário convoca um turbilhão ao redor de seu punho e ataca com velocidade cegante. Este movimento sempre age primeiro."}, "spicyExtract": {"name": "Spicy Extract", "effect": "O usuário emite um extrato incrivelmente picante, aumentando muito o Ataque do alvo e reduzindo muito a Defesa do alvo."}, "spinOut": {"name": "Spin Out", "effect": "O usuário gira furiosamente ao esticar as pernas, causando dano ao alvo. Isso também reduz muito a Velocidade do usuário."}, "populationBomb": {"name": "Population Bomb", "effect": "Os companheiros do usuário se reúnem em massa para executar um ataque combinado que atinge o alvo de uma a dez vezes seguidas."}, "iceSpinner": {"name": "<PERSON> Spinner", "effect": "O usuário cobre seus pés com gelo fino e gira ao redor, atingindo o alvo. O movimento giratório deste movimento também destrói o terreno."}, "glaiveRush": {"name": "<PERSON><PERSON><PERSON>", "effect": "O usuário lança todo o seu corpo em uma carga imprudente. Após o uso deste movimento, ataques contra o usuário não podem errar e infligirão o dobro do dano até a próxima vez que o usuário agir."}, "revivalBlessing": {"name": "Revival Blessing", "effect": "O usuário concede uma bên<PERSON> amorosa, reanimando um <PERSON> da equipe que tenha desmaiado e restaurando metade do máximo de PS desse Pokémon."}, "saltCure": {"name": "Salt Cure", "effect": "O usuário cura o alvo com sal, causando dano a cada turno. Tipos de Aço e Água são mais fortemente afetados por este movimento."}, "tripleDive": {"name": "Triple Dive", "effect": "O usuário executa um mergulho triplo perfeitamente cronometrado, atingindo o alvo com respingos de água três vezes seguidas."}, "mortalSpin": {"name": "Mortal Spin", "effect": "O usuário realiza um ataque giratório que também pode eliminar os efeitos de movimentos como Bind, Wrap e Leech Seed. <PERSON><PERSON> também envenena os Pokémon oponentes."}, "doodle": {"name": "Doodle", "effect": "O usuário captura a essência do alvo em um esboço. <PERSON><PERSON> muda as Habilidades do usuário e de seus Pokémon aliados para a do alvo."}, "filletAway": {"name": "Fillet Away", "effect": "O usuário aumenta muito seus atributos de Ataque, Ataque Esp. e Velocidade ao usar seus próprios PS."}, "kowtowCleave": {"name": "Kowtow Cleave", "effect": "O usuário corta o alvo depois de se curvar para fazer o alvo baixar a guarda. Este ataque nunca erra."}, "flowerTrick": {"name": "Flower Trick", "effect": "O usuário lança um buquê de flores armado no alvo. Este ataque nunca erra e sempre resulta em um acerto crítico."}, "torchSong": {"name": "<PERSON><PERSON>", "effect": "O usuário exala chamas furiosas como se estivesse cantando uma canção, queimando o alvo. Isso também aumenta o atributo de Ataque Esp. do usuário."}, "aquaStep": {"name": "Aqua Step", "effect": "O usuário brinca com o alvo e o ataca usando passos de dança leves e fluidos. Isso também aumenta a Velocidade do usuário."}, "ragingBull": {"name": "Raging Bull", "effect": "O usuário realiza um ataque de investida como um touro enfurecido. O tipo deste movimento depende da forma do usuário. Ele também pode quebrar barreira<PERSON>, como Light Screen e Reflect."}, "makeItRain": {"name": "Make It Rain", "effect": "O usuário ataca lançando uma massa de moedas. Is<PERSON> tamb<PERSON> reduz o atributo de Ataque Esp. do usuário. Dinheiro é ganho após a batalha."}, "psyblade": {"name": "Psyblade", "effect": "O usuário fende o alvo com uma lâmina etérea. O poder deste movimento é aumentado em 50% se o usuário estiver no Electric Terrain."}, "hydroSteam": {"name": "Hydro Steam", "effect": "O usuário ataca o alvo com água fervente. O poder deste movimento não é reduzido sob sol forte, mas sim aumentado em 50%."}, "ruination": {"name": "Ruination", "effect": "O usuário invoca um desastre ruinoso. Isso corta os PS do alvo pela metade."}, "collisionCourse": {"name": "Collision Course", "effect": "O usuário se transforma e cai no chão, causando uma explosão pré-histórica massiva. O poder deste movimento é aumentado mais do que o usual se for um golpe super eficaz."}, "electroDrift": {"name": "Electro Drift", "effect": "O usuário avança a velocidades ultra-rápidas, perfurando o alvo com eletricidade futurista. O poder deste movimento é aumentado mais do que o usual se for um golpe super eficaz."}, "shedTail": {"name": "<PERSON><PERSON>", "effect": "O usuário cria um substituto para si mesmo usando seus próprios PS antes de trocar de lugar com um Pokémon da equipe que está esperando."}, "chillyReception": {"name": "Chilly <PERSON>", "effect": "O usuário conta uma piada terrivelmente ruim antes de trocar de lugar com um Pokémon da equipe que está esperando. Isso invoca uma nevasca que dura cinco turnos."}, "tidyUp": {"name": "Tidy Up", "effect": "O usuário arruma e remove os efeitos de Spikes, Stealth Rock, Sticky Web, Toxic Spikes e Substitute. Isso também aumenta os atributos de Ataque e Velocidade do usuário."}, "snowscape": {"name": "Snowscape", "effect": "O usuário invoca uma tempestade de neve que dura cinco turnos. Isso aumenta os atributos de Defesa dos tipos Gelo."}, "pounce": {"name": "<PERSON><PERSON><PERSON>", "effect": "O usuário ataca saltando sobre o alvo. Isso também reduz a Velocidade do alvo."}, "trailblaze": {"name": "Trailblaze", "effect": "O usuário ataca repentinamente como se estivesse saltando de dentro da grama alta. A agilidade do usuário aumenta sua Velocidade."}, "chillingWater": {"name": "Chilling Water", "effect": "O usuário ataca o alvo derramando sobre ele água tão fria que suga seu poder. Isso também reduz o atributo de Ataque do alvo."}, "hyperDrill": {"name": "<PERSON>yper <PERSON>ill", "effect": "O usuário gira a parte pontiaguda de seu corpo em alta velocidade para perfurar o alvo. Este ataque pode atingir um alvo que esteja usando um movimento como Protect ou Detect."}, "twinBeam": {"name": "Twin Beam", "effect": "O usuário dispara feixes místicos de seus olhos para causar dano. O alvo é atingido duas vezes seguidas."}, "rageFist": {"name": "Rage Fist", "effect": "O usuário converte sua raiva em energia para atacar. Quanto mais vezes o usuário foi atingido por ataques, maior o poder do movimento."}, "armorCannon": {"name": "Armor Cannon", "effect": "O usuário dispara sua própria armadura como projéteis ardentes. Isso também reduz os atributos de Defesa e Defesa Esp. do usuário."}, "bitterBlade": {"name": "Bitter Blade", "effect": "O usuário concentra seus sentimentos amargos em relação ao mundo dos vivos em um ataque cortante. Os PS do usuário são restaurados em até metade do dano causado ao alvo."}, "doubleShock": {"name": "Double Shock", "effect": "O usuário descarrega toda a eletricidade de seu corpo para executar um ataque de alto dano. Após usar este movimento, o usuário não será mais do tipo Elétrico."}, "gigatonHammer": {"name": "Gigaton Hammer", "effect": "O usuário balança todo o seu corpo para atacar com seu enorme martelo. Este movimento não pode ser usado duas vezes seguidas."}, "comeuppance": {"name": "Comeuppance", "effect": "O usuário retalia com muito mais força contra o oponente que causou o último dano a ele."}, "aquaCutter": {"name": "Aqua Cutter", "effect": "O usuário expele água pressurizada para cortar o alvo como uma lâmina. Este movimento tem uma chance aumentada de resultar em um acerto crítico."}, "blazingTorque": {"name": "Blazing <PERSON>", "effect": "O usuário acelera seu motor ardente no alvo. <PERSON><PERSON> também pode deixar o alvo queimado."}, "wickedTorque": {"name": "Wicked Torque", "effect": "O usuário acelera seu motor no alvo com intenção maliciosa. <PERSON><PERSON> pode fazer o alvo adormecer."}, "noxiousTorque": {"name": "Noxious Torque", "effect": "O usuário acelera seu motor venenoso no alvo. <PERSON><PERSON> também pode envenenar o alvo."}, "combatTorque": {"name": "Combat Torque", "effect": "O usuário acelera seu motor com força no alvo. <PERSON><PERSON> também pode deixar o alvo paralisado."}, "magicalTorque": {"name": "Magical Torque", "effect": "O usuário acelera seu motor de fadas no alvo. <PERSON><PERSON> tamb<PERSON>m pode confundir o alvo."}, "bloodMoon": {"name": "Blood Moon", "effect": "O usuário libera toda a força de seu espírito de uma lua cheia que brilha tão vermelha quanto o sangue. Este movimento não pode ser usado duas vezes seguidas."}, "matchaGotcha": {"name": "<PERSON><PERSON>", "effect": "O usuário dispara um jato de chá que misturou. Os PS do usuário são restaurados em até metade do dano causado ao alvo. <PERSON><PERSON> também pode deixar o alvo queimado."}, "syrupBomb": {"name": "Syrup Bomb", "effect": "O usuário detona uma explosão de xarope de doces pegajoso, que reveste o alvo e faz o atributo de Velocidade do alvo cair a cada turno por três turnos."}, "ivyCudgel": {"name": "<PERSON>", "effect": "O usuário golpeia com um porrete envolto em hera. O tipo deste movimento muda dependendo da máscara usada pelo usuário, e tem uma chance aumentada de resultar em um acerto crítico."}, "electroShot": {"name": "Electro Shot", "effect": "O usuário acumula eletricidade no primeiro turno, aumentando suo atributo de Ataque Esp., e então dispara um tiro de alta voltagem no próximo turno. O tiro será disparado imediatamente na chuva."}, "teraStarstorm": {"name": "<PERSON>ra <PERSON>m", "effect": "Com o poder de seus cristais, o usuário bombardeia e elimina o alvo. Quando usado por Terapagos em sua Forma Estelar, este movimento causa dano a todos os Pokémon oponentes."}, "fickleBeam": {"name": "Fickle Beam", "effect": "O usuário dispara um feixe de luz para causar dano. Às vezes, todas as cabeças do usuário disparam feixes ao mesmo tempo, dobrando o poder do movimento."}, "burningBulwark": {"name": "Burning Bulwark", "effect": "A pele intensamente quente do usuário o protege de ataques e também queima qualquer atacante que faça contato direto."}, "thunderclap": {"name": "Thunderclap", "effect": "Este movimento permite que o usuário ataque primeiro com um choque de eletricidade. Este movimento falha se o alvo não estiver preparando um ataque."}, "mightyCleave": {"name": "Mighty Cleave", "effect": "O usuário empunha a luz que se acumulou no topo de sua cabeça para cortar o alvo. Este movimento atinge mesmo se o alvo se proteger."}, "tachyonCutter": {"name": "<PERSON><PERSON><PERSON>", "effect": "O usuário ataca lançando lâminas de partículas no alvo duas vezes seguidas. Este ataque nunca erra."}, "hardPress": {"name": "Hard Press", "effect": "O alvo é esmagado com um braço, uma garra ou algo do tipo para causar dano. Quanto mais PS o alvo tiver, maior o poder do movimento."}, "dragonCheer": {"name": "Dragon Cheer", "effect": "O usuário eleva o moral de seus aliados com um grito dracônico, para que seus futuros ataques tenham uma chance aumentada de resultar em golpes críticos. Isso anima mais os tipos Dragão."}, "alluringVoice": {"name": "Alluring Voice", "effect": "O usuário ataca o alvo usando sua voz angelical. <PERSON><PERSON> tamb<PERSON>m confunde o alvo se seus atributos tiverem sido aumentadas durante o turno."}, "temperFlare": {"name": "Temper Flare", "effect": "Impulsionado pelo desespero, o usuário ataca o alvo. O poder deste movimento é dobrado se o movimento anterior do usuário tiver falhado."}, "supercellSlam": {"name": "Supercell Slam", "effect": "O usuário eletrifica seu corpo e cai sobre o alvo para causar dano. Se este movimento errar, o usuário sofre dano."}, "psychicNoise": {"name": "Psychic Noise", "effect": "O usuário ataca o alvo com ondas sonoras desagradáveis. Por dois turnos, o alvo é impedido de recuperar PS através de movimentos, Habilidades ou itens mantidos."}, "upperHand": {"name": "Upper Hand", "effect": "O usuário reage ao movimento do alvo e o ataca com o calcanhar da palma da mão, fazendo o alvo hesitar. Este movimento falha se o alvo não estiver preparando um movimento de prioridade."}, "malignantChain": {"name": "Malignant Chain", "effect": "O usuário derrama toxinas no alvo envolvendo-o em uma corrente tóxica e corrosiva. <PERSON><PERSON> também pode deixar o alvo seriamente envenenado."}}