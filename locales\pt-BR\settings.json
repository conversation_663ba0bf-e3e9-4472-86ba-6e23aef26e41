{"boy": "Men<PERSON>", "girl": "Menina", "general": "G<PERSON>", "display": "Exibição", "audio": "<PERSON><PERSON><PERSON>", "gamepad": "Controle", "keyboard": "Teclado", "gameSpeed": "Velocidade do Jogo", "gameSpeed100x": "1×", "gameSpeed125x": "1.25×", "gameSpeed150x": "1.5×", "gameSpeed200x": "2×", "gameSpeed250x": "2.5×", "gameSpeed300x": "3×", "gameSpeed400x": "4×", "gameSpeed500x": "5×", "hpBarSpeed": "Velocidade da Barra de PS", "expGainsSpeed": "Velocidade do Ganho de EXP", "expPartyDisplay": "Exibição de EXP da Equipe", "skipSeenDialogues": "Pular Diálogos Vistos", "eggSkip": "Pular Eclosões de Ovos", "never": "Nunca", "always": "Sempre", "ask": "Pergun<PERSON>", "battleStyle": "<PERSON><PERSON><PERSON>", "enableRetries": "Habilitar Novas Tentativas", "hideIvs": "Esconder scanner de IV", "tutorials": "Tutorial", "touchControls": "Controles de Toque", "vibrations": "Vibração", "normal": "Normal", "fast": "<PERSON><PERSON><PERSON><PERSON>", "faster": "<PERSON><PERSON>", "skip": "<PERSON><PERSON>", "levelUpNotifications": "Notificação", "on": "Ligado", "off": "Des<PERSON><PERSON>", "switch": "Alternar", "set": "Definido", "auto": "Automático", "disabled": "Desativado", "language": "Idioma", "change": "<PERSON><PERSON>", "uiTheme": "<PERSON><PERSON>face", "default": "Padrão", "legacy": "<PERSON><PERSON>", "windowType": "<PERSON><PERSON><PERSON>", "moneyFormat": "Formatação do Dinheiro", "damageNumbers": "Números de Dano", "simple": "Simples", "fancy": "<PERSON><PERSON><PERSON><PERSON>", "abbreviated": "Abreviado", "moveAnimations": "Animações de Movimento", "showStatsOnLevelUp": "Mostrar Atributos ao Subir de Nível", "candyUpgradeNotification": "<PERSON><PERSON><PERSON> com <PERSON>e", "passivesOnly": "Passivas", "candyUpgradeDisplay": "<PERSON><PERSON> com Doce", "icon": "Ícone", "animation": "Animação", "moveInfo": "Informações de Movimento", "showMovesetFlyout": "Mostrar Flutuante de Movimentos", "showArenaFlyout": "Mostrar Flutuante de Bioma", "showTimeOfDayWidget": "Widget da Hora do Dia", "timeOfDayAnimation": "Animação da Hora do Dia", "bounce": "Saltar", "timeOfDayBack": "Voltar", "spriteSet": "Sprites", "consistent": "Consistente", "experimental": "Experimental", "fusionPaletteSwaps": "Cores da Paleta de Fusão", "playerGender": "Gênero do Jogador", "typeHints": "Dicas de Tipo", "masterVolume": "Volume Mestre", "bgmVolume": "Músicas", "fieldVolume": "Ambiente", "seVolume": "<PERSON><PERSON><PERSON>", "uiVolume": "Interface", "battleMusic": "Música de Batalha", "musicGenFive": "Gen V", "musicAllGens": "<PERSON><PERSON> as <PERSON><PERSON>.", "gamepadPleasePlug": "Conecte um controle ou pressione um botão", "delete": "Deletar", "keyboardPleasePress": "Pressione uma tecla", "reset": "Redefinir", "requireReload": "<PERSON><PERSON>", "action": "Ação", "back": "Voltar", "pressToBind": "Pressione para Atribuir", "pressButton": "Pressione um Botão…", "assignButton": "Atribuir Bo<PERSON>ão", "buttonUp": "Cima", "buttonDown": "Baixo", "buttonLeft": "E<PERSON>rda", "buttonRight": "<PERSON><PERSON><PERSON>", "buttonAction": "Ação", "buttonMenu": "<PERSON><PERSON>", "buttonSubmit": "Confirmar", "buttonCancel": "<PERSON><PERSON><PERSON>", "buttonStats": "Atributos", "buttonCycleForm": "Próxima Forma", "buttonCycleShiny": "Próximo <PERSON>", "buttonCycleGender": "<PERSON>ró<PERSON><PERSON>", "buttonCycleAbility": "Próxima Habilidade", "buttonCycleNature": "Próxima Natureza", "buttonCycleVariant": "Próxima <PERSON>", "buttonCycleTera": "Ciclar Tipo Tera", "buttonSpeedUp": "<PERSON><PERSON><PERSON>", "buttonSlowDown": "Desacelerar", "alt": " (Alt)", "mute": "<PERSON><PERSON>", "controller": "Controle", "controllerDefault": "Padrão", "controllerChange": "<PERSON><PERSON>", "gamepadSupport": "Suporte para Controle", "gamepadSupportAuto": "Auto", "gamepadSupportDisabled": "Desativado", "pressActionToAssign": "Pressione ação para atribuir", "willSwapWith": "trocará com", "confirmSwap": "Confirmar troca", "cancelControllerChoice": "<PERSON><PERSON><PERSON>", "showBgmBar": "Exibir Nomes das Músicas", "hideUsername": "<PERSON><PERSON><PERSON><PERSON>", "moveTouchControls": "Mover <PERSON><PERSON> de Toque", "touchReset": "Redefinir", "touchSaveClose": "Salvar & sair", "touchCancel": "<PERSON><PERSON><PERSON>", "orientation": "Orientação:", "landscape": "Paisagem", "portrait": "Retrato", "shopOverlayOpacity": "Opacidade da Loja", "shopCursorTarget": "Alvo do Cursor da Loja", "commandCursorMemory": "Memória do Cursor de Batalha", "rewards": "<PERSON><PERSON>", "reroll": "<PERSON><PERSON><PERSON><PERSON>", "shop": "<PERSON><PERSON>", "checkTeam": "Checar Time", "confirmDisableTouch": "Tem certeza que quer desativar os Controles de Toque?", "defaultConfirmMessage": "Tem certeza?"}