{"blockRecoilDamage": "{{abilityName}}\nde {{pokemonName}} le protège du contrecoup !", "badDreams": "{{poke<PERSON><PERSON><PERSON>}} a le sommeil agité !", "costar": "{{pokemonName}} copie les changements de stats\nde {{allyName}} !", "iceFaceAvoidedDamage": "{{pokemonNameWithAffix}} évite les dégâts\navec {{abilityName}} !", "perishBody": "{{abilityName}} de {{pokemonName}}\nmettra les deux Pokémon K.O. dans trois tours !", "poisonHeal": "{{abilityName}} de {{pokemonName}}\nrestaure un peu ses PV !", "trace": "{{pokemonName}} copie le talent {{abilityName}}\nde {{targetName}} !", "windPowerCharged": "{{pokemonName}} a été touché par la capacité {{moveName}} et se charge en électricité !", "quickDraw": "Tir Vif permet à {{pokemonName}}\nd’agir plus vite que d’habitude !", "illusionBreak": "L’illusion de {{pokemon<PERSON>ame}} se brise !", "disguiseAvoidedDamage": "Le déguisement de {{pokemonNameWithAffix}}\ntombe !", "blockItemTheft": "{{abilityName}} de {{pokemonNameWithAffix}}\nempêche son objet d’être volé !", "typeImmunityHeal": "{{abilityName}} de {{pokemonNameWithAffix}}\nrestaure un peu ses PV !", "nonSuperEffectiveImmunity": "{{pokemonNameWithAffix}} évite\nles dégâts avec {{abilityName}} !", "fullHpResistType": "{{pokemonNameWithAffix}} fait briller sa carapace\net fausse les affinités de type !", "moveImmunity": "Ça n’affecte pas {{pokemonNameWithAffix}}…", "reverseDrain": "{{pokemonNameWithAffix}} aspire\nle suintement !", "postDefendTypeChange": "{{abilityName}} de {{pokemonNameWithAffix}}\nle transforme en type {{typeName}} !", "postDefendContactDamage": "{{pokemonNameWithAffix}} a blessé\nson attaquant !", "postDefendAbilitySwap": "{{pokemonNameWithAffix}} et sa cible\néchangent leurs talents !", "postDefendAbilityGive": "{{pokemonNameWithAffix}} donne\nle talent {{abilityName}} à sa cible !", "postDefendMoveDisable": "La capacité {{moveName}}\nde {{pokemonNameWithAffix}} est mise sous entrave !", "pokemonTypeChange": "{{pokemonNameWithAffix}} prend\nle type {{moveType}} !", "pokemonTypeChangeRevert": "{{pokemonNameWithAffix}} a repris\nson type d’origine !", "postAttackStealHeldItem": "{{pokemonNameWithAffix}} vole\nl’objet {{stolenItemType}} de {{defenderName}} !", "postDefendStealHeldItem": "{{pokemonNameWithAffix}} vole\nl’objet {{stolenItemType}} de {{attackerName}} !", "copyFaintedAllyAbility": "{{pokemonNameWithAffix}} reçoit\nle talent {{abilityName}} !", "intimidateImmunity": "{{abilityName}} de {{pokemonNameWithAffix}}\nl’empêche d’être intimidé !", "postSummonAllyHeal": "{{pokemonNameWithAffix}} boit le thé\npréparé par {{pokemonName}} !", "postSummonClearAllyStats": "Les stats de {{pokemonNameWithAffix}}\nsont revenues à la normale !", "postSummonTransform": "{{pokemonNameWithAffix}} prend\nl’apparence de {{targetName}} !", "protectStat": "{{abilityName}} de {{pokemonNameWithAffix}}\nempêche {{statName}} de baisser !", "statusEffectImmunityWithName": "{{abilityName}} de {{pokemonNameWithAffix}}\nl’empêche d’être {{statusEffectName}} !", "statusEffectImmunity": "{{abilityName}} de {{pokemonNameWithAffix}}\nempêche tout problème de statut !", "battlerTagImmunity": "{{abilityName}} de {{pokemonNameWithAffix}}\nempêche {{battlerTagName}} !", "typeImmunityPowerBoost": "{{pokemonNameWithAffix}} augmente la puissance\nde ses capacités de type {{typeName}} !", "forewarn": "{{pokemonNameWithAffix}} détecte\nla capacité {{moveName}} !", "frisk": "{{pokemonNameWithAffix}} fouille {{opponentName}}\net trouve son talent {{opponentAbilityName}} !", "postWeatherLapseHeal": "{{abilityName}} de {{pokemonNameWithAffix}}\nrestaure un peu ses PV !", "postWeatherLapseDamage": "{{pokemonNameWithAffix}} est blessé\npar son talent {{abilityName}} !", "postTurnLootCreateEatenBerry": "{{pokemonNameWithAffix}} a récolté\nune {{berryName}} !", "postTurnHeal": "{{abilityName}} de {{pokemonNameWithAffix}}\nrestaure un peu ses PV !", "fetchBall": "{{pokemonNameWithAffix}} trouve\nune {{pokeballName}} !", "healFromBerryUse": "{{abilityName}} de {{pokemonNameWithAffix}}\nrestaure un peu ses PV !", "arenaTrap": "{{pokemonNameWithAffix}} empêche\nles changements grâce à son talent {{abilityName}} !", "postBattleLoot": "{{pokemonNameWithAffix}} ramasse\nl’objet {{itemName}} !", "postFaintContactDamage": "{{pokemonNameWithAffix}} a blessé\nson attaquant !", "postFaintHpDamage": "{{pokemonNameWithAffix}} a blessé\nson attaquant !", "postSummonPressure": "{{pokemonNameWithAffix}}\naugmente la pression !", "weatherEffectDisappeared": "Les effets de la météo se dissipent !", "postSummonMoldBreaker": "{{pokemonNameWithAffix}}\nbrise le moule !", "postSummonAnticipation": "{{pokemonNameWithAffix}}\nest tout tremblant !", "postSummonTurboblaze": "{{pokemonNameWithAffix}} dégage\nune aura de flammes incandescentes !", "postSummonTeravolt": "{{pokemonNameWithAffix}} dégage\nune aura électrique instable !", "postSummonDarkAura": "{{pokemonNameWithAffix}} dégage\nune aura ténébreuse !", "postSummonFairyAura": "{{pokemonNameWithAffix}} dégage\nune aura enchanteresse !", "postSummonAuraBreak": "{{pokemonNameWithAffix}} inverse\ntoutes les auras !", "postSummonNeutralizingGas": "Le gaz inhibiteur {{pokemonNameWithAffix}}\nenvahit les lieux !", "postSummonAsOneGlastrier": "{{pokemonNameWithAffix}}\na deux talents !", "postSummonAsOneSpectrier": "{{pokemonNameWithAffix}}\na deux talents !", "postSummonVesselOfRuin": "<PERSON>’Urne du Fléau de {{pokemonNameWithAffix}}\naffaiblit l’{{statName}} des Pokémon alentour !", "postSummonSwordOfRuin": "<PERSON>’É<PERSON><PERSON> du Fléau de {{pokemonNameWithAffix}}\naffaiblit la {{statName}} des Pokémon alentour !", "postSummonTabletsOfRuin": "Le Bois du Fléau de {{pokemonNameWithAffix}}\naffaiblit l’{{statName}} des Pokémon alentour !", "postSummonBeadsOfRuin": "Les Perles du Fléau de {{pokemonNameWithAffix}}\naffaiblissent la {{statName}} des Pokémon alentour !", "preventBerryUse": "{{pokemonNameWithAffix}} est tendu\net ne peut plus manger de <PERSON> !"}