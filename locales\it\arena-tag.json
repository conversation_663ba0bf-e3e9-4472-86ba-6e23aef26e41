{"mistOnAdd": "La nebbia ha avvolto entrambe le squadre!", "mistOnAddPlayer": "La nebbia ha avvolto la tua squadra!", "mistOnAddEnemy": "La nebbia ha avvolto la squadra avversaria!", "mistOnRemove": "L’effetto della nebbia è finito per entrambe le squadre!", "mistOnRemovePlayer": "L’effetto della nebbia è finito per la tua squadra!", "mistOnRemoveEnemy": "L’effetto della nebbia è finito per la squadra avversaria!", "mistApply": "{{pokemonNameWithAffix}} è protetto\ndalla nebbia!", "reflectOnAdd": "Riflesso ha aumentato la resistenza di entrambe le squadre agli attacchi fisici!", "reflectOnAddPlayer": "Riflesso ha aumentato la resistenza della tua squadra agli attacchi fisici!", "reflectOnAddEnemy": "Riflesso ha aumentato la resistenza della squadra avversaria agli attacchi fisici!", "lightScreenOnAdd": "Scher<PERSON><PERSON>ce ha aumentato la resistenza di entrambe le squadre agli attacchi speciali!", "lightScreenOnAddPlayer": "Scher<PERSON><PERSON>ce ha aumentato la resistenza della tua squadra agli attacchi speciali!", "lightScreenOnAddEnemy": "Scher<PERSON>luce ha aumentato la resistenza della squadra avversaria agli attacchi speciali!", "auroraVeilOnAdd": "Velaurora ha aumentato la resistenza di entrambe le squadre agli attacchi fisici e speciali!", "auroraVeilOnAddPlayer": "Velaurora ha aumentato la resistenza della tua squadra agli attacchi fisici e speciali!", "auroraVeilOnAddEnemy": "Velaurora ha aumentato la resistenza della squadra avversaria agli attacchi fisici e speciali!", "conditionalProtectOnAdd": "{{moveName}} ha protetto entrambe le squadre!", "conditionalProtectOnAddPlayer": "La tua squadra è protetta da {{moveName}}!", "conditionalProtectOnAddEnemy": "La squadra avversaria è protetta da {{moveName}}!", "conditionalProtectApply": "{{pokemonNameWithAffix}} è protetto\nda {{moveName}}!", "matBlockOnAdd": "{{pokemonNameWithAffix}} si prepara\na respingere gli attacchi!", "matBlockApply": "<PERSON><PERSON><PERSON><PERSON><PERSON> protegge da {{attackName}}!", "noCritOnAdd": "<PERSON>un<PERSON><PERSON> protegge entrambe le squadre dai brutti colpi!", "noCritOnAddPlayer": "<PERSON>uncan<PERSON> protegge la tua squadra dai brutti colpi!", "noCritOnAddEnemy": "Fortuncanto protegge la squadra avversaria dai brutti colpi!", "noCritOnRemove": "L’effetto di Fortuncanto è finito per entrambe le squadre!", "noCritOnRemovePlayer": "L’effetto di Fortuncanto è finito per la tua squadra!", "noCritOnRemoveEnemy": "L’effetto di Fortuncanto è finito per la squadra avversaria!", "wishTagOnAdd": "Il desiderio di {{pokemonNameWithAffix}}\nsi avvera!", "mudSportOnAdd": "La potenza delle mosse di tipo Elettro diminuisce!", "mudSportOnRemove": "L’effetto di Fangata è svanito!", "waterSportOnAdd": "La potenza delle mosse di tipo Fuoco diminuisce!", "waterSportOnRemove": "L’effetto di Docciascudo è svanito!", "plasmaFistsOnAdd": "Una pioggia di elettroni si rovescia sui Pokémon!", "spikesOnAdd": "Ai piedi di entrambe le squadre c’è una trappola di punte!", "spikesOnAddPlayer": "Ai piedi della tua squadra c’è una trappola di punte!", "spikesOnAddEnemy": "Ai piedi della squadra avversaria c’è una trappola di punte!", "spikesOnRemove": "Ai piedi di entrambe le squadre non c’è più la trappola di punte!", "spikesOnRemovePlayer": "Ai piedi della tua squadra non c’è più la trappola di punte!", "spikesOnRemoveEnemy": "Ai piedi della squadra avversaria non c’è più la trappola di punte!", "spikesActivateTrap": "{{pokemonNameWithAffix}} è colpito da Punte!", "toxicSpikesOnAdd": "Ai piedi di entrambe le squadre c’è una trappola di punte velenose!", "toxicSpikesOnAddPlayer": "Ai piedi della tua squadra c’è una trappola di punte velenose!", "toxicSpikesOnAddEnemy": "Ai piedi della squadra avversaria c’è una trappola di punte velenose!", "toxicSpikesOnRemove": "Ai piedi di entrambe le squadre non c’è più la trappola di punte velenose!", "toxicSpikesOnRemovePlayer": "Ai piedi della tua squadra non c’è più la trappola di punte velenose!", "toxicSpikesOnRemoveEnemy": "Ai piedi della squadra avversaria non c’è più la trappola di punte velenose!", "stealthRockOnAdd": "Entrambe le squadre sono circondate da rocce aguzze sospese in aria!", "stealthRockOnAddPlayer": "La tua squadra è circondata da rocce aguzze sospese in aria!", "stealthRockOnAddEnemy": "La squadra avversaria è circondata da rocce aguzze sospese in aria!", "stealthRockOnRemove": "Levitoroccia non ha più effetto su entrame le squadre!", "stealthRockOnRemovePlayer": "Levitoroccia non ha più effetto sulla tua squadra!", "stealthRockOnRemoveEnemy": "Levitoroccia non ha più effetto sulla squadra avversaria!", "stealthRockActivateTrap": "Rocce aguzze colpiscono\n{{pokemonNameWithAffix}}!", "stickyWebOnAdd": "Una Rete Vischiosa viene stesa ai piedi di entrambe le squadre!", "stickyWebOnAddPlayer": "Una Rete Vischiosa viene stesa ai piedi della tua squadra!", "stickyWebOnAddEnemy": "Una Rete Vischiosa viene stesa ai piedi della squadra avversaria!", "stickyWebOnRemove": "La Rete Vischiosa ai piedi di entrambe le squadre svanisce!", "stickyWebOnRemovePlayer": "La Rete Vischiosa ai piedi della tua squadra svanisce!", "stickyWebOnRemoveEnemy": "La Rete Vischiosa ai piedi della squadra avversaria svanisce!", "stickyWebActivateTrap": "{{pokemonNameWithAffix}} rimane impigliato nella Rete Vischiosa!", "trickRoomOnAdd": "{{pokemonNameWithAffix}} crea\nuna dimensione distorta!", "trickRoomOnRemove": "La dimensione distorta\ntorna alla normalità!", "wonderRoomOnAdd": "La Difesa e la Difesa Speciale sono state invertite!", "wonderRoomOnRemove": "La Difesa e la Difesa Speciale sono ritornate normali!", "gravityOnAdd": "La gravità si intensifica!", "gravityOnRemove": "La gravità torna normale!", "tailwindOnAdd": "Comincia a soffiare Ventoincoda su entrambe le squadre!", "tailwindOnAddPlayer": "Comincia a soffiare Ventoincoda sulla tua squadra!", "tailwindOnAddEnemy": "Comincia a soffiare Ventoincoda sulla squadra avversaria!", "tailwindOnRemove": "Ventoincoda non soffia più su entrambe le squadre!", "tailwindOnRemovePlayer": "Ventoincoda non soffia più sulla tua squadra!", "tailwindOnRemoveEnemy": "Ventoincoda non soffia più sulla squadra avversaria!", "happyHourOnAdd": "Una sensazione di euforia si diffonde nell’aria!", "happyHourOnRemove": "L’atmosfera torna alla normalità.", "safeguardOnAdd": "Un velo mistico ricopre il campo!", "safeguardOnAddPlayer": "Un velo mistico ricopre la tua squadra!", "safeguardOnAddEnemy": "Un velo mistico ricopre la squadra avversaria!", "safeguardOnRemove": "Il campo non è più protetto da Salvaguardia!", "safeguardOnRemovePlayer": "La tua squadra non è più protetta da Salvaguardia!", "safeguardOnRemoveEnemy": "La squadra avversaria non è più protetta da Salvaguardia!", "fireGrassPledgeOnAdd": "Il campo di battaglia è circondato dalle fiamme!", "fireGrassPledgeOnAddPlayer": "La tua squadra è circondata dalle fiamme!", "fireGrassPledgeOnAddEnemy": "La squadra avversaria è circondata dalle fiamme!", "fireGrassPledgeOnRemove": "Il mare di fiamme è sparito!", "fireGrassPledgeOnRemovePlayer": "La tua squadra non è più circondata dalle fiamme!", "fireGrassPledgeOnRemoveEnemy": "La squadra avversaria non è più circondata dalle fiamme!", "fireGrassPledgeLapse": "{{pokemonNameWithAffix}} è ferito da un mare di fiamme!", "waterFirePledgeOnAdd": "Appare un arcobaleno nel cielo!", "waterFirePledgeOnAddPlayer": "Appare un arcobaleno sulla tua squadra!", "waterFirePledgeOnAddEnemy": "Appare un arcobaleno sulla squadra avversaria!", "waterFirePledgeOnRemove": "L’arcobaleno è sparito!", "waterFirePledgeOnRemovePlayer": "L’arcobaleno sulla tua squadra scompare!", "waterFirePledgeOnRemoveEnemy": "L’arcobaleno sulla squadra avversaria scompare!", "grassWaterPledgeOnAdd": "Il campo di battaglia è circondato da una palude!", "grassWaterPledgeOnAddPlayer": "La tua squadra è circondata da una palude!", "grassWaterPledgeOnAddEnemy": "La squadra avversaria è circondata da una palude!", "grassWaterPledgeOnRemove": "La palude è sparita!", "grassWaterPledgeOnRemovePlayer": "La tua squadra non è più circondata dalla palude!", "grassWaterPledgeOnRemoveEnemy": "La squadra avversaria non è più circondata dalla palude!", "fairyLockOnAdd": "Nel prossimo turno sarà impossibile scappare!", "neutralizingGasOnAdd": "Il Gas Reagente si diffonde tutt’intorno!", "neutralizingGasOnRemove": "<PERSON>’effetto del Gas Reagente svanisce!"}