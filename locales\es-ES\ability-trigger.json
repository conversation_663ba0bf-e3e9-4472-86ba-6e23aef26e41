{"blockRecoilDamage": "¡{{abilityName}} de {{pokemonName}}\nlo protegió del daño de retroceso!", "badDreams": "¡{{pokemon<PERSON>ame}} está atormentado!", "costar": "¡{{pokemonName}} copió los cambios de características de {{allyName}}!", "iceFaceAvoidedDamage": "¡{{pokemonNameWithAffix}} evit<PERSON>\nda<PERSON> con {{abilityName}}!", "perishBody": "¡{{abilityName}} de {{pokemonName}} debilitará a ambos Pokémon en 3 turnos!", "poisonHeal": "¡{{pokemonName}} restauró algunos de sus PS gracias a {{abilityName}}!", "trace": "¡{{pokemonName}} ha copiado la habilidad {{abilityName}} \nde {{targetName}}!", "windPowerCharged": "¡{{pokemonName}} se ha cargado de electricidad gracias a {{moveName}}!", "quickDraw": "¡{{pokemon<PERSON>ame}} ataca primero gracias a la habilidad Mano Rápida!", "illusionBreak": "¡La ilusión de {{pokemonName}} se ha desvanecido!", "disguiseAvoidedDamage": "¡El disfraz de {{pokemonNameWithAffix}} se ha roto!", "blockItemTheft": "¡{{pokemonNameWithAffix}} evitó el robo gracias a {{abilityName}}!", "typeImmunityHeal": "¡{{pokemonNameWithAffix}} restauró algunos de sus PS gracias a {{abilityName}}!", "nonSuperEffectiveImmunity": "¡{{pokemonNameWithAffix}} evitó el daño gracias a {{abilityName}}!", "fullHpResistType": "¡{{pokemonNameWithAffix}} ha hecho brillar su caparazón\ny ha alterado su compatibilidad entre tipos!", "moveImmunity": "¡No afecta a {{pokemonNameWithAffix}}!", "reverseDrain": "¡{{pokemonNameWithAffix}} absorbió lodo líquido!", "postDefendTypeChange": "¡{{abilityName}} de {{pokemonNameWithAffix}} cambió a tipo {{typeName}}!", "postDefendContactDamage": "¡{{abilityName}} de {{pokemonNameWithAffix}} ha herido a su atacante!", "postDefendAbilitySwap": "¡{{pokemonNameWithAffix}} intercambió su habilidad con su objetivo!", "postDefendAbilityGive": "¡{{pokemonNameWithAffix}} cambió la habilidad del objetivo por {{abilityName}}!", "postDefendMoveDisable": "¡{{moveName}} de {{pokemonNameWithAffix}} ha sido anulado!", "pokemonTypeChange": "¡{{pokemonNameWithAffix}} ha cambiado a tipo {{moveType}}!", "pokemonTypeChangeRevert": "¡{{pokemonNameWithAffix}} ha recobrado\nsu tipo original!", "postAttackStealHeldItem": "¡{{pokemonNameWithAffix}} robó {{stolenItemType}} de {{defenderName}}!", "postDefendStealHeldItem": "¡{{pokemonNameWithAffix}} robó {{stolenItemType}} de {{attackerName}}!", "copyFaintedAllyAbility": "¡{{abilityName}} de {{pokemonNameWithAffix}} fue copiada!", "intimidateImmunity": "¡{{abilityName}} de {{pokemonNameWithAffix}} evita que sea intimidado!", "postSummonAllyHeal": "¡{{pokemonNameWithAffix}} se ha bebido el té que ha preparado {{pokemonName}}!", "postSummonClearAllyStats": "¡Los cambios de características de {{pokemonNameWithAffix}} fueron eliminados!", "postSummonTransform": "¡{{pokemonNameWithAffix}} se transformó en {{targetName}}!", "protectStat": "¡{{abilityName}} de {{pokemonNameWithAffix}} evita que baje su {{statName}}!", "statusEffectImmunityWithName": "¡{{abilityName}} de {{pokemonNameWithAffix}} evita {{statusEffectName}}!", "statusEffectImmunity": "¡{{abilityName}} de {{pokemonNameWithAffix}} evita los problemas de estado!", "battlerTagImmunity": "¡{{abilityName}} de {{pokemonNameWithAffix}} previene {{battlerTagName}}!", "typeImmunityPowerBoost": "¡La potencia de los movimientos de tipo {{typeName}}\nde {{pokemonNameWithAffix}} ha aumentado!", "forewarn": "¡{{pokemonNameWithAffix}} ha detectado el movimiento {{moveName}}!", "frisk": "¡{{pokemonNameWithAffix}} ha cacheado {{opponentAbilityName}} de {{opponentName}}!", "postWeatherLapseHeal": "¡{{pokemonNameWithAffix}} restauró algunos de sus PS gracias a {{abilityName}}!", "postWeatherLapseDamage": "¡{{pokemonNameWithAffix}} se hizo daño por su {{abilityName}}!", "postTurnLootCreateEatenBerry": "¡{{pokemonNameWithAffix}} recogió una {{berryName}}!", "postTurnHeal": "¡{{pokemonNameWithAffix}} restauró algunos de sus PS gracias a {{abilityName}}!", "fetchBall": "¡{{pokemonNameWithAffix}} encontró {{pokeballName}}!", "healFromBerryUse": "¡{{pokemonNameWithAffix}} se curó gracias a {{abilityName}}!", "arenaTrap": "¡{{pokemonNameWithAffix}} impide el cambio con {{abilityName}}!", "postBattleLoot": "¡{{pokemonNameWithAffix}} recogió {{itemName}}!", "postFaintContactDamage": "¡{{abilityName}} de {{pokemonNameWithAffix}} hizo daño a su atacante!", "postFaintHpDamage": "¡{{abilityName}} de {{pokemonNameWithAffix}} hizo daño a su atacante!", "postSummonPressure": "¡{{pokemonNameWithAffix}} ejerce Presión!", "weatherEffectDisappeared": "El tiempo atmosférico ya no ejerce ninguna influencia.", "postSummonMoldBreaker": "¡{{pokemonNameWithAffix}} rompió el molde!", "postSummonAnticipation": "¡{{pokemonNameWithAffix}} se anticipó!", "postSummonTurboblaze": "¡{{pokemonNameWithAffix}} irradia un aura llameante!", "postSummonTeravolt": "¡{{pokemonNameWithAffix}} irradia un aura chisporroteante!", "postSummonDarkAura": "¡{{pokemonNameWithAffix}} irradia un aura oscura!", "postSummonFairyAura": "¡{{pokemonNameWithAffix}} irradia un aura feérica!", "postSummonAuraBreak": "¡{{pokemonNameWithAffix}} ha invertido todas las auras!", "postSummonNeutralizingGas": "¡El Gas Reactivo de {{pokemonNameWithAffix}} se propaga por toda la zona!", "postSummonAsOneGlastrier": "¡{{pokemonNameWithAffix}} tiene dos Habilidades!", "postSummonAsOneSpectrier": "¡{{pokemonNameWithAffix}} tiene dos Habilidades!", "postSummonVesselOfRuin": "¡{{pokemonNameWithAffix}} ha mermado {{statName}} de los demás Pokémon con Caldero Debacle!", "postSummonSwordOfRuin": "¡{{pokemonNameWithAffix}} ha mermado {{statName}} de los demás Pokémon con Espada Debacle!", "postSummonTabletsOfRuin": "¡{{pokemonNameWithAffix}} ha mermado {{statName}} de los demás Pokémon con Ta<PERSON><PERSON><PERSON> Debacle!", "postSummonBeadsOfRuin": "¡{{pokemonNameWithAffix}} ha mermado {{statName}} de los demás Pokémon con Abalorio Debacle!", "preventBerryUse": "¡{{pokemonNameWithAffix}} está muy nervioso y no puede comer bayas!"}