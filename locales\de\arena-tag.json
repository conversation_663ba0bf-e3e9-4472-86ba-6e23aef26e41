{"mistOnAdd": "Pokémon werden in Weißnebel gehüllt!", "mistOnAddPlayer": "<PERSON>, die auf deiner Seite kämpfen, werden in Weißnebel gehüllt!", "mistOnAddEnemy": "<PERSON>, die auf der gegnerischen Seite kämpfen, werden in Weißnebel gehüllt!", "mistOnRemove": "<PERSON> Weiß<PERSON>bel, der die Pokémon umgab, hat sich gelichtet!", "mistOnRemovePlayer": "<PERSON>bel, der die Pokémon auf deiner Seite umgab, hat sich gelichtet!", "mistOnRemoveEnemy": "<PERSON> Weißnebel, der die gegnerischen Pokémon umgab, hat sich gelichtet!", "mistApply": "Der Weißnebel verhindert die Senkung der Statuswerte von {{pokemonNameWithAffix}}!", "reflectOnAdd": "Reflektor stärkt alle Pokémon gegen physische Attacken!", "reflectOnAddPlayer": "Reflektor stärkt Pokémon auf deiner Seite gegen physische Attacken!", "reflectOnAddEnemy": "Reflektor stärkt gegnerische Pokémon gegen physische Attacken!", "lightScreenOnAdd": "Lichtschild stärkt alle Pokémon gegen Spezial-Attacken!", "lightScreenOnAddPlayer": "Lichtschild stärkt Pokémon, die auf deiner Seite kämpfen, gegen Spezial-Attacken!", "lightScreenOnAddEnemy": "Lichtschild stärkt gegnerische Pokémon gegen Spezial-Attacken!", "auroraVeilOnAdd": "Auroraschleier stärkt Pokémon gegen physische und Spezial-Attacken!", "auroraVeilOnAddPlayer": "Auroraschleier stärkt Pokémon auf deiner Seite gegen physische und Spezial-Attacken!", "auroraVeilOnAddEnemy": "Auroraschleier stärkt gegnerische Pokémon gegen physische und Spezial-Attacken!", "conditionalProtectOnAdd": "Die Pokémon werden von {{moveName}} behütet!", "conditionalProtectOnAddPlayer": "Die Pokémon auf deiner Seite werden von {{moveName}} behütet!", "conditionalProtectOnAddEnemy": "Die Pokémon auf der gegnerischen Seite werden von {{moveName}} behütet!", "conditionalProtectApply": "{{pokemonNameWithAffix}} wird durch {{moveName}} geschützt!", "matBlockOnAdd": "{{pokemonNameWithAffix}} bringt seinen Tatami-<PERSON><PERSON><PERSON> in Position!", "matBlockApply": "{{attackName}} wurde durch den Tatami-Schild abgewehrt!", "noCritOnAdd": "Beschwörung schützt alle vor Volltreffern!", "noCritOnAddPlayer": "Beschwörung schützt dein Team vor Volltreffern!", "noCritOnAddEnemy": "Beschwörung schützt das gegnerische Team vor Volltreffern!", "noCritOnRemove": "Beschwörung hört auf zu wirken!", "noCritOnRemovePlayer": "Beschwörung deines Teams hört auf zu wirken!", "noCritOnRemoveEnemy": "Beschwörung des gegnerischen Teams hört auf zu wirken!", "wishTagOnAdd": "Der Wunschtraum von {{pokemonNameWithAffix}} erfüllt sich!", "mudSportOnAdd": "Die Stärke aller Elektro-Attacken wurde reduziert!", "mudSportOnRemove": "<PERSON><PERSON><PERSON><PERSON> hört auf zu wirken!", "waterSportOnAdd": "Die Stärke aller Feuer-Attacken wurde reduziert!", "waterSportOnRemove": "Nassma<PERSON> hört auf zu wirken!", "plasmaFistsOnAdd": "Ein elektrisch geladener Niederschlag regnet auf das Kampffeld herab!", "spikesOnAdd": "Die Pokémon sind von Stacheln umgeben!", "spikesOnAddPlayer": "Die Pokémon auf deiner Seite sind von Stacheln umgeben!", "spikesOnAddEnemy": "Die Pokémon auf gegnerischer Seite sind von Stacheln umgeben!", "spikesOnRemove": "Die Stacheln, die herumlagen, sind verschwunden!", "spikesOnRemovePlayer": "<PERSON> Stacheln, die auf deiner Seite herumlagen, sind verschwunden!", "spikesOnRemoveEnemy": "Die Stacheln, die auf der gegnerischen Seite herumlagen, sind verschwunden!", "spikesActivateTrap": "{{pokemonNameWithAffix}} wurde durch Stachler verletzt!", "toxicSpikesOnAdd": "Die Pokémon sind von giftigen Stacheln umgeben!", "toxicSpikesOnAddPlayer": "Die Pokémon auf deiner Seite sind von giftigen Stacheln umgeben!", "toxicSpikesOnAddEnemy": "Die Pokémon auf gegnerischer Seite sind von giftigen Stacheln umgeben!", "toxicSpikesOnRemove": "Die giftigen Stacheln, die herumlagen, sind verschwunden!", "toxicSpikesOnRemovePlayer": "Die giftigen Stacheln, die auf deiner Seite herumlagen, sind verschwunden!", "toxicSpikesOnRemoveEnemy": "Die giftigen Stacheln, die auf der gegnerischen Seite herumlagen, sind verschwunden!", "stealthRockOnAdd": "Um die Pokémon schweben spitze <PERSON>!", "stealthRockOnAddPlayer": "Um die Pokémon auf deiner Seite schweben spitze <PERSON>!", "stealthRockOnAddEnemy": "Um die Pokémon auf gegnerischer Seite schweben spitze <PERSON>!", "stealthRockOnRemove": "Die spitzen Steine um die Pokémon sind verschwunden!", "stealthRockOnRemovePlayer": "Die spitzen Steine um die Pokémon auf deiner Seite sind verschwunden!", "stealthRockOnRemoveEnemy": "Die spitzen Steine um die Pokémon auf der gegnerischen Seite sind verschwunden!", "stealthRockActivateTrap": "{{pokemonNameWithAffix}} wird von spitzen <PERSON> getroffen!", "stickyWebOnAdd": "Am Boden um die Pokémon entspinnt sich ein Klebenetz!", "stickyWebOnAddPlayer": "Am Boden um die Pokémon auf deiner Seite entspinnt sich ein Klebenetz!", "stickyWebOnAddEnemy": "Am Boden um die Pokémon auf der gegnerischen Seite entspinnt sich ein Klebenetz!", "stickyWebOnRemove": "Das Klebenetz ist wieder verschwunden!\t", "stickyWebOnRemovePlayer": "Das Klebenetz auf deiner Seite ist wieder verschwunden!", "stickyWebOnRemoveEnemy": "Das Klebenetz auf der gegnerischen Seite ist wieder verschwunden!", "stickyWebActivateTrap": "{{pokemonNameWithAffix}} ist im Klebenetz gefangen!", "trickRoomOnAdd": "{{pokemonNameWithAffix}} hat die Dimensionen verdreht!", "trickRoomOnRemove": "Die verdrehte Dimension ist wieder normal!", "wonderRoomOnAdd": "Es entsteht ein <PERSON>, in dem Verteidigung und Spezial-Verteidigung miteinander vertauscht sind!", "wonderRoomOnRemove": "Der Wunderraum verpufft. Verteidigung und Spezial-Verteidigung werden wieder zurückgesetzt!", "gravityOnAdd": "Die Erdanziehung wurde verstärkt!", "gravityOnRemove": "Die Erdanziehung ist wieder normal!", "tailwindOnAdd": "Die Pokémon erhalten Rückenwind!", "tailwindOnAddPlayer": "Die Pokémon, die auf deiner Seite kämpfen, erhalten Rückenwind!", "tailwindOnAddEnemy": "Die gegnerischen Pokémon erhalten Rückenwind!", "tailwindOnRemove": "Der Rückenwind hat sich gelegt!", "tailwindOnRemovePlayer": "Der Rückenwind auf deiner Seite hat sich gelegt!", "tailwindOnRemoveEnemy": "Der Rückenwind auf gegnerischer Seite hat sich gelegt!", "happyHourOnAdd": "Goldene Zeiten sind angebrochen!", "happyHourOnRemove": "Die goldenen Zeiten sind vorbei!", "safeguardOnAdd": "Das ganze Feld wird von einem Schleier umhüllt!", "safeguardOnAddPlayer": "Das Team des Anwenders wird von einem Schleier umhüllt!", "safeguardOnAddEnemy": "Das gegnerische Team wird von einem Schleier umhüllt!", "safeguardOnRemove": "Der mystische Schleier, der das ganze Feld umgab, hat sich gelüftet!", "safeguardOnRemovePlayer": "Der mystische Schleier, der dein Team umgab, hat sich gelüftet!", "safeguardOnRemoveEnemy": "Der mystische Schleier, der das gegnerische Team umgab, hat sich gelüftet!", "fireGrassPledgeOnAdd": "Es erstreckt sich ein Me<PERSON> aus Feuer!", "fireGrassPledgeOnAddPlayer": "Um die Pokémon auf deiner Se<PERSON> erstreckt sich ein Meer aus Feuer!", "fireGrassPledgeOnAddEnemy": "Um die Pokémon auf der gegnerischen Seite erstreckt sich ein Me<PERSON> aus Feuer!", "fireGrassPledgeOnRemove": "Das Meer aus Feuer um die Pokémon ist verschwunden!", "fireGrassPledgeOnRemovePlayer": "Das Meer aus Feuer um die Pokémon auf deiner Seite ist verschwunden!", "fireGrassPledgeOnRemoveEnemy": "Das Meer aus Feuer um die Pokémon auf der gegnerischen Seite ist verschwunden!", "fireGrassPledgeLapse": "{{pokemonNameWithAffix}} nimmt Schaden durch das Meer aus Feuer!", "waterFirePledgeOnAdd": "Ein Regenbogen erscheint am Himmel!", "waterFirePledgeOnAddPlayer": "Ein Regenbogen erscheint am Himmel über den Pokémon auf deiner Seite!", "waterFirePledgeOnAddEnemy": "Ein Regenbogen erscheint am Himmel über den Pokémon auf der gegnerischen Seite!", "waterFirePledgeOnRemove": "Der Regenbogen über den Pokémon ist verschwunden!", "waterFirePledgeOnRemovePlayer": "Der Regenbogen über den Pokémon auf deiner Seite ist verschwunden!", "waterFirePledgeOnRemoveEnemy": "Der Regenbogen über den Pokémon auf der gegnerischen Seite ist verschwunden!", "grassWaterPledgeOnAdd": "Ein Sumpf tut sich auf!", "grassWaterPledgeOnAddPlayer": "Ein Sumpf tut sich um die Pokémon auf deiner Seite auf!", "grassWaterPledgeOnAddEnemy": "Ein Sumpf tut sich um die Pokémon auf der gegnerischen Seite auf!", "grassWaterPledgeOnRemove": "Der Sumpf um die Pokémon ist verschwunden!", "grassWaterPledgeOnRemovePlayer": "Der Sumpf um die Pokémon auf deiner Seite ist verschwunden!", "grassWaterPledgeOnRemoveEnemy": "Der Sumpf um die Pokémon auf der gegnerischen Seite ist verschwunden!", "fairyLockOnAdd": "Während der nächsten Runde ist keine Flucht möglich!", "neutralizingGasOnAdd": "Reaktionsgas von {{pokemonNameWithAffix}} hat sich in der Umgebung ausgebreitet!", "neutralizingGasOnRemove": "Das Reaktionsgas hört auf zu wirken!"}